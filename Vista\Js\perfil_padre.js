document.addEventListener("DOMContentLoaded", () => {
    console.log("perfil.js cargado y DOMContentLoaded disparado.");

    // Referencias a elementos del DOM
    const editButtons = document.querySelectorAll(".edit-section-btn")
    const changePasswordBtn = document.querySelector(".change-password-btn")
    const changeAvatarBtn = document.querySelector(".change-avatar-btn")
    const modalCloseButtons = document.querySelectorAll(".modal-close-btn")
    const modals = document.querySelectorAll(".modal-overlay")
    
    // Función para mostrar modal según la sección
    editButtons.forEach((button) => {
      button.addEventListener("click", () => {
        const section = button.getAttribute("data-section")
        const modal = document.getElementById(`edit-${section}-modal`)
        if (modal) {
          modal.classList.add("active")
        }
      })
    })
    
    // Mostrar modal de cambio de contraseña
    if (changePasswordBtn) {
      changePasswordBtn.addEventListener("click", () => {
        const modal = document.getElementById("change-password-modal")
        if (modal) {
          modal.classList.add("active")
        }
      })
    }
    
    // Manejar cambio de avatar
    if (changeAvatarBtn) {
      changeAvatarBtn.addEventListener("click", () => {
        const fileInput = document.createElement("input")
        fileInput.type = "file"
        fileInput.accept = "image/*"
        fileInput.style.display = "none"
    
        fileInput.addEventListener("change", (e) => {
          if (e.target.files && e.target.files[0]) {
            const file = e.target.files[0]
            const formData = new FormData()
            formData.append('foto_perfil', file)
            formData.append('tipo_actualizacion', 'avatar')
    
            // Deshabilitar el botón de envío
            const originalText = changeAvatarBtn.textContent
            changeAvatarBtn.disabled = true
            changeAvatarBtn.innerHTML = '<span class="material-icons">hourglass_empty</span> Subiendo...'
    
            fetch('perfil_p.php', { // Apunta a perfil_p.php para padres
              method: 'POST',
              body: formData
            })
            .then(response => {
              console.log('Status de respuesta avatar:', response.status)
              return response.json()
            })
            .then(data => {
              console.log('Respuesta completa del servidor avatar:', data)
              if (data.success) {
                alert(data.message || 'Foto de perfil actualizada correctamente')
                // Actualizar la imagen en la interfaz
                const profileAvatarImg = document.querySelector('.profile-avatar-container img')
                const sidebarAvatarImg = document.querySelector('.user-info .user-avatar img')
                const defaultAvatars = document.querySelectorAll('.default-avatar')
    
                if (profileAvatarImg) {
                  profileAvatarImg.src = data.new_image_url + '?' + new Date().getTime() // Añadir timestamp para evitar caché
                } else {
                  // Si no hay img, crearla y reemplazar el default-avatar
                  defaultAvatars.forEach(avatarDiv => {
                    const img = document.createElement('img')
                    img.src = data.new_image_url + '?' + new Date().getTime()
                    img.alt = 'Foto de perfil'
                    avatarDiv.innerHTML = '' // Limpiar iniciales
                    avatarDiv.classList.remove('default-avatar', 'padre') // Remover clases de avatar por defecto
                    avatarDiv.appendChild(img)
                  })
                }
    
                if (sidebarAvatarImg) {
                  sidebarAvatarImg.src = data.new_image_url + '?' + new Date().getTime()
                }
    
              } else {
                alert('Error al actualizar la foto de perfil: ' + (data.message || 'Error desconocido'))
              }
            })
            .catch(error => {
              console.error('Error de red al subir avatar:', error)
              alert('Error de conexión al subir la foto: ' + error.message)
            })
            .finally(() => {
              changeAvatarBtn.disabled = false
              changeAvatarBtn.innerHTML = originalText
            })
          }
        })
    
        document.body.appendChild(fileInput)
        fileInput.click()
        document.body.removeChild(fileInput)
      })
    }
    
    // Cerrar modales con botones de cierre
    modalCloseButtons.forEach((button) => {
      button.addEventListener("click", () => {
        const modal = button.closest(".modal-overlay")
        if (modal) {
          modal.classList.remove("active")
        }
      })
    })
    
    // Cerrar modales al hacer clic fuera del contenido
    modals.forEach((modal) => {
      modal.addEventListener("click", (e) => {
        if (e.target === modal) {
          modal.classList.remove("active")
        }
      })
    })
    
    // Manejar envío de formularios
    const forms = document.querySelectorAll(".edit-form")
    forms.forEach((form) => {
      form.addEventListener("submit", (e) => {
        console.log("Formulario enviado - Evento submit capturado.");
        e.preventDefault()

        // Determinar el tipo de formulario basado en el modal padre
        const modal = form.closest(".modal-overlay")
        let tipoActualizacion = ''

        if (modal.id === 'edit-personal-modal') {
          tipoActualizacion = 'personal'
        } else if (modal.id === 'edit-account-modal') {
          tipoActualizacion = 'account'
        } else if (modal.id === 'edit-emergency-modal') {
          tipoActualizacion = 'emergency'
        } else if (modal.id === 'change-password-modal') {
          tipoActualizacion = 'password'
        }

        if (!tipoActualizacion) {
          alert('Error: No se pudo determinar el tipo de formulario')
          return
        }

        // Validaciones específicas para cambio de contraseña
        if (tipoActualizacion === 'password') {
          const nuevaContrasena = form.querySelector('[name="nueva_contrasena"]').value
          const confirmarContrasena = form.querySelector('[name="confirmar_contrasena"]').value

          if (nuevaContrasena !== confirmarContrasena) {
            alert('Las contraseñas no coinciden')
            return
          }

          // Validar fortaleza de contraseña
          if (nuevaContrasena.length < 8) {
            alert('La contraseña debe tener al menos 8 caracteres')
            return
          }

          if (!/[A-Z]/.test(nuevaContrasena)) {
            alert('La contraseña debe contener al menos una letra mayúscula')
            return
          }

          if (!/[0-9]/.test(nuevaContrasena)) {
            alert('La contraseña debe contener al menos un número')
            return
          }

          if (!/[^a-zA-Z0-9]/.test(nuevaContrasena)) {
            alert('La contraseña debe contener al menos un carácter especial')
            return
          }
        }

        // Crear FormData con los datos del formulario
        const formData = new FormData(form)
        formData.append('tipo_actualizacion', tipoActualizacion)

        // Deshabilitar el botón de envío para evitar envíos múltiples
        const submitBtn = form.querySelector('button[type="submit"]')
        const originalText = submitBtn.textContent
        submitBtn.disabled = true
        submitBtn.textContent = 'Guardando...'

        // Debug: Mostrar datos que se van a enviar
        console.log('Tipo de actualización:', tipoActualizacion)
        console.log('Datos del formulario:')
        for (let pair of formData.entries()) {
          console.log(pair[0] + ': ' + pair[1])
        }

        // Enviar datos al mismo archivo PHP
        fetch('perfil_p.php', { // Apunta a perfil_p.php para padres
          method: 'POST',
          body: formData
        })
        .then(response => {
          console.log('Status de respuesta:', response.status)
          console.log('Headers de respuesta:', response.headers)
          return response.text()
        })
        .then(text => {
          console.log('Respuesta completa del servidor:', text)

          // Verificar si la respuesta está vacía
          if (!text.trim()) {
            alert('Error: El servidor no devolvió ninguna respuesta')
            return
          }

          try {
            const data = JSON.parse(text)
            console.log('Datos parseados:', data)

            if (data.success) {
              alert(data.message || 'Cambios guardados correctamente')
              // Cerrar modal
              if (modal) {
                modal.classList.remove("active")
              }
              // Recargar la página para mostrar los cambios
              setTimeout(() => {
                location.reload()
              }, 1000)
            } else {
              alert('Error: ' + (data.message || 'Error desconocido'))
            }
          } catch (e) {
            console.error('Error parsing JSON:', e)
            console.error('Texto recibido:', text)
            alert('Error en la respuesta del servidor. Ver consola para detalles.')
          }
        })
        .catch(error => {
          console.error('Error de red:', error)
          alert('Error de conexión: ' + error.message)
        })
        .finally(() => {
          // Rehabilitar el botón
          submitBtn.disabled = false
          submitBtn.textContent = originalText
        })
      })
    })
  })