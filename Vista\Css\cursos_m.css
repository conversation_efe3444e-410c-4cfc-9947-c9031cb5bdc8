/* Estilos específicos para la página de cursos de maestros */

/* Variables */
:root {
    --primary-color: #2a4db7;
    --primary-light: #e6f0ff;
    --secondary-color: #f4f7fc;
    --text-color: #333;
    --text-light: #666;
    --border-color: #e0e0e0;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
  
    /* Colores para íconos de cursos */
    --math-color: #2196f3;
    --science-color: #4caf50;
    --language-color: #9c27b0;
    --english-color: #00bcd4;
    --history-color: #e91e63;
    --art-color: #ff9800;
    --pe-color: #ff5722;
    --music-color: #795548;
  }
  
  /* Filtro y acciones de cursos */
  .courses-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 20px;
  }
  
  .filter-container {
    display: flex;
    align-items: center;
    gap: 15px;
    background-color: white;
    border-radius: 10px;
    padding: 15px 20px;
    box-shadow: var(--shadow-sm);
    flex: 1;
    min-width: 300px;
    flex-wrap: wrap;
  }
  
  .search-box {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 10px;
    background-color: var(--secondary-color);
    border-radius: 5px;
    padding: 10px 15px;
    min-width: 200px;
  }
  
  .search-box .material-icons {
    color: var(--text-light);
  }
  
  .search-box input {
    flex: 1;
    border: none;
    background: none;
    font-size: 0.9rem;
    color: var(--text-color);
    outline: none;
    width: 100%;
  }
  
  .filter-options {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
  }
  
  .filter-options select {
    padding: 10px 15px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 0.9rem;
    color: var(--text-color);
    background-color: white;
    outline: none;
    cursor: pointer;
  }
  
  .create-course-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
  }
  
  .create-course-btn:hover {
    background-color: #1e40af;
    transform: translateY(-2px);
  }
  
  /* Lista de cursos */
  .courses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 25px;
  }
  
  .course-card {
    background-color: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    border: 1px solid var(--border-color);
  }
  
  .course-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
  }
  
  .course-card-header {
    height: 160px;
    position: relative;
    overflow: hidden;
  }
  
  .course-card-header img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }
  
  .course-card:hover .course-card-header img {
    transform: scale(1.05);
  }
  
  .course-icon {
    position: absolute;
    bottom: 15px;
    left: 15px;
    width: 50px;
    height: 50px;
    border-radius: 10px;
    background-color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-sm);
    z-index: 1;
  }
  
  .course-icon .material-icons {
    color: white;
    font-size: 1.8rem;
  }
  
  .course-actions {
    position: absolute;
    top: 10px;
    right: 10px;
    display: flex;
    gap: 5px;
    opacity: 0;
    transition: var(--transition);
    z-index: 10; /* Asegurar que esté por encima de otros elementos */
  }

  .course-card:hover .course-actions {
    opacity: 1;
  }
  
  .course-action-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: white;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
  }
  
  .course-action-btn:hover {
    transform: translateY(-2px);
  }
  
  .edit-btn:hover {
    background-color: var(--primary-light);
    color: var(--primary-color);
  }
  
  .delete-btn:hover {
    background-color: #ffebee;
    color: var(--danger-color);
  }
  
  .course-card-body {
    padding: 20px;
    flex: 1;
  }
  
  .course-card-body h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--text-color);
  }
  
  .course-grade {
    font-size: 1rem;
    color: var(--text-light);
    margin-bottom: 15px;
  }
  
  .course-details {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 15px;
  }
  
  .course-detail {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    color: var(--text-light);
  }
  
  .course-detail .material-icons {
    font-size: 1.1rem;
    color: var(--primary-color);
  }
  
  .course-card-footer {
    padding: 15px 20px;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: center;
  }
  
  .view-course-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background-color: var(--primary-light);
    color: var(--primary-color);
    border-radius: 5px;
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
  }
  
  .view-course-btn:hover {
    background-color: var(--primary-color);
    color: white;
  }
  
  /* Modal para crear/editar curso */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }
  
  .modal-overlay.active {
    display: flex;
  }
  
  .modal-content {
    background-color: white;
    border-radius: 10px;
    width: 90%;
    max-width: 700px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: var(--shadow-lg);
    animation: modalFadeIn 0.3s ease-out;
  }
  
  .modal-small {
    max-width: 500px;
  }
  
  @keyframes modalFadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid var(--border-color);
  }
  
  .modal-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
  }
  
  .modal-close-btn {
    background: none;
    border: none;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
  }
  
  .modal-close-btn:hover {
    background-color: var(--secondary-color);
    color: var(--text-color);
  }
  
  .modal-body {
    padding: 25px;
  }
  
  /* Formulario */
  .form-group {
    margin-bottom: 20px;
  }
  
  .form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
  }
  
  .form-row .form-group {
    flex: 1;
    margin-bottom: 0;
  }
  
  .form-group label {
    display: block;
    font-weight: 500;
    margin-bottom: 8px;
    color: var(--text-color);
  }
  
  .form-group input[type="text"],
  .form-group input[type="number"],
  .form-group select,
  .form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 0.95rem;
    color: var(--text-color);
    font-family: inherit;
    outline: none;
    transition: var(--transition);
  }
  
  .form-group input:focus,
  .form-group select:focus,
  .form-group textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-light);
  }
  
  .schedule-inputs {
    display: flex;
    flex-direction: column;
    gap: 15px;
    background-color: var(--secondary-color);
    padding: 15px;
    border-radius: 5px;
  }
  
  .schedule-day {
    display: flex;
    align-items: center;
    gap: 15px;
  }
  
  .schedule-day label {
    min-width: 100px;
    margin-bottom: 0;
  }
  
  .time-inputs {
    display: flex;
    align-items: center;
    gap: 10px;
  }
  
  .time-inputs input[type="time"] {
    padding: 8px 10px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 0.9rem;
    color: var(--text-color);
    outline: none;
  }
  
  .file-upload {
    position: relative;
  }
  
  .file-upload input[type="file"] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
  }
  
  .file-upload-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 15px;
    border: 1px dashed var(--border-color);
    border-radius: 5px;
    color: var(--text-light);
    transition: var(--transition);
  }
  
  .file-upload:hover .file-upload-btn {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background-color: var(--primary-light);
  }
  
  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    padding: 20px 25px;
    border-top: 1px solid var(--border-color);
  }
  
  .btn-secondary,
  .btn-primary,
  .btn-danger {
    padding: 12px 20px;
    border-radius: 5px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
  }
  
  .btn-secondary {
    background-color: var(--secondary-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
  }
  
  .btn-secondary:hover {
    background-color: #e0e0e0;
  }
  
  .btn-primary {
    background-color: var(--primary-color);
    color: white;
    border: none;
  }
  
  .btn-primary:hover {
    background-color: #1e40af;
  }
  
  .btn-danger {
    background-color: var(--danger-color);
    color: white;
    border: none;
  }
  
  .btn-danger:hover {
    background-color: #d32f2f;
  }

/* === ESTILOS PARA MOSTRAR HORARIO EN TARJETA === */
.course-schedule {
  margin-top: 15px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding-top: 15px;
  border-top: 1px solid var(--border-color);
}

.schedule-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.85rem;
  color: var(--text-light);
}

.schedule-item .material-icons {
  font-size: 1.1rem;
  color: var(--primary-color);
}

  /* Responsive */
  @media (max-width: 992px) {
    .courses-actions {
      flex-direction: column;
      align-items: stretch;
    }
  
    .filter-container {
      width: 100%;
    }
  
    .create-course-btn {
      align-self: flex-end;
    }
  
    .form-row {
      flex-direction: column;
      gap: 20px;
    }
  
    .schedule-day {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;
    }
  }
  
  @media (max-width: 768px) {
    .filter-options {
      width: 100%;
      margin-top: 10px;
    }
  
    .filter-options select {
      flex: 1;
    }
  
    .courses-grid {
      grid-template-columns: 1fr;
    }
  
    .create-course-btn {
      width: 100%;
      justify-content: center;
    }
  
    .form-actions {
      flex-direction: column;
    }
  
    .btn-secondary,
    .btn-primary,
    .btn-danger {
      width: 100%;
    }
  }