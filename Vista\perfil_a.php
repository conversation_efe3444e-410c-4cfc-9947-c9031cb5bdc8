<?php
error_log("DEBUG: perfil_a.php - Archivo cargado.");
require_once '../Controlador/AuthController.php';

error_log("DEBUG: perfil_a.php - REQUEST_METHOD: " . $_SERVER['REQUEST_METHOD']);
error_log("DEBUG: perfil_a.php - POST data received: " . print_r($_POST, true));
error_log("DEBUG: perfil_a.php - tipo_actualizacion set: " . (isset($_POST['tipo_actualizacion']) ? 'true' : 'false'));
require_once '../Modelo/Administrador.php';
require_once '../Modelo/Conexion.php';

// Proteger la página - solo administradores
AuthController::protegerPagina(['administrador']);

// Procesar solicitudes AJAX de actualización de perfil
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['tipo_actualizacion'])) {
    header('Content-Type: application/json');

    // Log para debugging
    error_log("PERFIL UPDATE: Iniciando procesamiento - " . date('Y-m-d H:i:s'));
    error_log("PERFIL UPDATE: POST data - " . print_r($_POST, true));

    try {
        $pdo = Conexion::getConexion();
        $usuarioActual = AuthController::obtenerUsuarioActual();

        if (!$usuarioActual) {
            throw new Exception('Usuario no autenticado');
        }

        $tipoActualizacion = $_POST['tipo_actualizacion'];
        error_log("PERFIL UPDATE: Tipo - $tipoActualizacion, Usuario ID - " . $usuarioActual['id']);

        switch ($tipoActualizacion) {
            case 'personal':
                // Actualizar información personal
                $nombres = trim($_POST['nombres'] ?? '');
                $apellido_paterno = trim($_POST['apellido_paterno'] ?? '');
                $apellido_materno = trim($_POST['apellido_materno'] ?? '');
                $dni = trim($_POST['dni'] ?? '');
                $fecha_nacimiento = $_POST['fecha_nacimiento'] ?? null;
                $sexo = $_POST['sexo'] ?? '';
                $direccion = trim($_POST['direccion'] ?? '');
                $telefono = trim($_POST['telefono'] ?? '');

                if (empty($nombres) || empty($apellido_paterno) || empty($apellido_materno)) {
                    throw new Exception('Los nombres y apellidos son obligatorios');
                }

                // Calcular edad si hay fecha de nacimiento
                $edad = null;
                if (!empty($fecha_nacimiento)) {
                    $fechaNac = new DateTime($fecha_nacimiento);
                    $hoy = new DateTime();
                    $edad = $hoy->diff($fechaNac)->y;
                }

                $pdo->beginTransaction();

                $sql = "UPDATE personas SET
                            nombres = :nombres,
                            apellido_paterno = :apellido_paterno,
                            apellido_materno = :apellido_materno,
                            dni = :dni,
                            fecha_nacimiento = :fecha_nacimiento,
                            edad = :edad,
                            sexo = :sexo,
                            direccion = :direccion,
                            telefono = :telefono,
                            updated_at = CURRENT_TIMESTAMP
                        WHERE usuario_id = :usuario_id";

                error_log("PERFIL UPDATE PERSONAL: SQL - " . $sql);
                error_log("PERFIL UPDATE PERSONAL: Params - " . json_encode([
                    'nombres' => $nombres,
                    'apellido_paterno' => $apellido_paterno,
                    'apellido_materno' => $apellido_materno,
                    'dni' => $dni ?: null,
                    'fecha_nacimiento' => $fecha_nacimiento ?: null,
                    'edad' => $edad,
                    'sexo' => $sexo,
                    'direccion' => $direccion,
                    'telefono' => $telefono,
                    'usuario_id' => $usuarioActual['id']
                ]));
                $stmt = $pdo->prepare($sql);
                $stmt->execute([
                    ':nombres' => $nombres,
                    ':apellido_paterno' => $apellido_paterno,
                    ':apellido_materno' => $apellido_materno,
                    ':dni' => $dni ?: null,
                    ':fecha_nacimiento' => $fecha_nacimiento ?: null,
                    ':edad' => $edad,
                    ':sexo' => $sexo,
                    ':direccion' => $direccion,
                    ':telefono' => $telefono,
                    ':usuario_id' => $usuarioActual['id']
                ]);

                // Actualizar sesión
                $_SESSION['nombres'] = $nombres;
                $_SESSION['apellido_paterno'] = $apellido_paterno;
                $_SESSION['apellido_materno'] = $apellido_materno;

                $pdo->commit();
                error_log("PERFIL UPDATE: Información personal actualizada exitosamente para usuario ID " . $usuarioActual['id']);
                echo json_encode(['success' => true, 'message' => 'Información personal actualizada correctamente']);
                break;

            case 'account':
                // Actualizar información de cuenta
                $nombre_usuario = trim($_POST['nombre_usuario'] ?? '');
                $email = trim($_POST['email'] ?? '');

                if (empty($nombre_usuario) || empty($email)) {
                    throw new Exception('El nombre de usuario y email son obligatorios');
                }

                if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                    throw new Exception('El formato del email no es válido');
                }

                // Verificar unicidad
                $sqlCheck = "SELECT id FROM usuarios WHERE nombre_usuario = :nombre_usuario AND id != :usuario_id";
                $stmtCheck = $pdo->prepare($sqlCheck);
                $stmtCheck->execute([':nombre_usuario' => $nombre_usuario, ':usuario_id' => $usuarioActual['id']]);
                if ($stmtCheck->fetch()) {
                    throw new Exception('El nombre de usuario ya está en uso');
                }

                $sqlCheck = "SELECT id FROM usuarios WHERE email = :email AND id != :usuario_id";
                $stmtCheck = $pdo->prepare($sqlCheck);
                $stmtCheck->execute([':email' => $email, ':usuario_id' => $usuarioActual['id']]);
                if ($stmtCheck->fetch()) {
                    throw new Exception('El email ya está en uso');
                }

                $pdo->beginTransaction();

                $sql = "UPDATE usuarios SET
                            nombre_usuario = :nombre_usuario,
                            email = :email,
                            updated_at = CURRENT_TIMESTAMP
                        WHERE id = :usuario_id";

                error_log("PERFIL UPDATE ACCOUNT: SQL - " . $sql);
                error_log("PERFIL UPDATE ACCOUNT: Params - " . json_encode([
                    ':nombre_usuario' => $nombre_usuario,
                    ':email' => $email,
                    ':usuario_id' => $usuarioActual['id']
                ]));
                $stmt = $pdo->prepare($sql);
                $stmt->execute([
                    ':nombre_usuario' => $nombre_usuario,
                    ':email' => $email,
                    ':usuario_id' => $usuarioActual['id']
                ]);

                // Actualizar sesión
                $_SESSION['nombre_usuario'] = $nombre_usuario;
                $_SESSION['email'] = $email;

                $pdo->commit();
                error_log("PERFIL UPDATE: Información de cuenta actualizada exitosamente para usuario ID " . $usuarioActual['id']);
                echo json_encode(['success' => true, 'message' => 'Información de cuenta actualizada correctamente']);
                break;

            case 'professional':
                // Actualizar información profesional
                if ($usuarioActual['rol'] !== 'administrador') {
                    throw new Exception('No tienes permisos para esta acción');
                }

                $cargo = trim($_POST['cargo'] ?? '');
                $departamento = trim($_POST['departamento'] ?? '');
                $fecha_contratacion = $_POST['fecha_contratacion'] ?? null;

                if (empty($cargo)) {
                    throw new Exception('El cargo es obligatorio');
                }

                $pdo->beginTransaction();

                // Obtener persona_id
                $sqlPersona = "SELECT id FROM personas WHERE usuario_id = :usuario_id";
                $stmtPersona = $pdo->prepare($sqlPersona);
                $stmtPersona->execute([':usuario_id' => $usuarioActual['id']]);
                $persona = $stmtPersona->fetch();

                if (!$persona) {
                    throw new Exception('No se encontró información de persona');
                }

                $sql = "UPDATE administradores SET
                            cargo = :cargo,
                            departamento = :departamento,
                            fecha_contratacion = :fecha_contratacion,
                            updated_at = CURRENT_TIMESTAMP
                        WHERE persona_id = :persona_id";

                error_log("PERFIL UPDATE PROFESSIONAL: SQL - " . $sql);
                error_log("PERFIL UPDATE PROFESSIONAL: Params - " . json_encode([
                    ':cargo' => $cargo,
                    ':departamento' => $departamento,
                    ':fecha_contratacion' => $fecha_contratacion ?: null,
                    ':persona_id' => $persona['id']
                ]));
                $stmt = $pdo->prepare($sql);
                $stmt->execute([
                    ':cargo' => $cargo,
                    ':departamento' => $departamento,
                    ':fecha_contratacion' => $fecha_contratacion ?: null,
                    ':persona_id' => $persona['id']
                ]);

                $pdo->commit();
                echo json_encode(['success' => true, 'message' => 'Información profesional actualizada correctamente']);
                break;

            case 'emergency':
                // Actualizar contacto de emergencia
                $nombre_contacto = trim($_POST['nombre_contacto'] ?? '');
                $telefono_principal = trim($_POST['telefono_principal'] ?? '');
                $telefono_alternativo = trim($_POST['telefono_alternativo'] ?? '');
                $email_contacto = trim($_POST['email'] ?? '');

                if (empty($nombre_contacto) || empty($telefono_principal)) {
                    throw new Exception('El nombre del contacto y teléfono principal son obligatorios');
                }

                if (!empty($email_contacto) && !filter_var($email_contacto, FILTER_VALIDATE_EMAIL)) {
                    throw new Exception('El formato del email no es válido');
                }

                $pdo->beginTransaction();

                // Obtener persona_id
                $sqlPersona = "SELECT id FROM personas WHERE usuario_id = :usuario_id";
                $stmtPersona = $pdo->prepare($sqlPersona);
                $stmtPersona->execute([':usuario_id' => $usuarioActual['id']]);
                $persona = $stmtPersona->fetch();

                if (!$persona) {
                    throw new Exception('No se encontró información de persona');
                }

                // Verificar si ya existe un contacto
                $sqlCheck = "SELECT id FROM contactos_emergencia WHERE persona_id = :persona_id";
                $stmtCheck = $pdo->prepare($sqlCheck);
                $stmtCheck->execute([':persona_id' => $persona['id']]);
                $existeContacto = $stmtCheck->fetch();

                if ($existeContacto) {
                    // Actualizar
                    $sql = "UPDATE contactos_emergencia SET
                                nombre_contacto = :nombre_contacto,
                                telefono_principal = :telefono_principal,
                                telefono_alternativo = :telefono_alternativo,
                                email = :email,
                                updated_at = CURRENT_TIMESTAMP
                            WHERE persona_id = :persona_id";
                } else {
                    // Crear nuevo
                    $sql = "INSERT INTO contactos_emergencia
                            (persona_id, nombre_contacto, telefono_principal, telefono_alternativo, email, tipo_contacto_id)
                            VALUES (:persona_id, :nombre_contacto, :telefono_principal, :telefono_alternativo, :email, 5)";
                }

                error_log("PERFIL UPDATE EMERGENCY: SQL - " . $sql);
                error_log("PERFIL UPDATE EMERGENCY: Params - " . json_encode([
                    ':persona_id' => $persona['id'],
                    ':nombre_contacto' => $nombre_contacto,
                    ':telefono_principal' => $telefono_principal,
                    ':telefono_alternativo' => $telefono_alternativo ?: null,
                    ':email' => $email_contacto ?: null
                ]));
                $stmt = $pdo->prepare($sql);
                $stmt->execute([
                    ':persona_id' => $persona['id'],
                    ':nombre_contacto' => $nombre_contacto,
                    ':telefono_principal' => $telefono_principal,
                    ':telefono_alternativo' => $telefono_alternativo ?: null,
                    ':email' => $email_contacto ?: null
                ]);

                $pdo->commit();
                echo json_encode(['success' => true, 'message' => 'Contacto de emergencia actualizado correctamente']);
                break;

            case 'password':
                // Cambiar contraseña
                $contrasena_actual = $_POST['contrasena_actual'] ?? '';
                $nueva_contrasena = $_POST['nueva_contrasena'] ?? '';
                $confirmar_contrasena = $_POST['confirmar_contrasena'] ?? '';

                if (empty($contrasena_actual) || empty($nueva_contrasena) || empty($confirmar_contrasena)) {
                    throw new Exception('Todos los campos son obligatorios');
                }

                if ($nueva_contrasena !== $confirmar_contrasena) {
                    throw new Exception('Las contraseñas no coinciden');
                }

                if (strlen($nueva_contrasena) < 8) {
                    throw new Exception('La contraseña debe tener al menos 8 caracteres');
                }

                // Verificar contraseña actual
                $sqlUsuario = "SELECT password FROM usuarios WHERE id = :usuario_id";
                $stmtUsuario = $pdo->prepare($sqlUsuario);
                $stmtUsuario->execute([':usuario_id' => $usuarioActual['id']]);
                $usuario = $stmtUsuario->fetch();

                if (!$usuario || !password_verify($contrasena_actual, $usuario['password'])) {
                    throw new Exception('La contraseña actual es incorrecta');
                }

                $pdo->beginTransaction();

                $nueva_contrasena_hash = password_hash($nueva_contrasena, PASSWORD_DEFAULT);
                $sql = "UPDATE usuarios SET
                            password = :password,
                            updated_at = CURRENT_TIMESTAMP
                        WHERE id = :usuario_id";

                error_log("PERFIL UPDATE PASSWORD: SQL - " . $sql);
                error_log("PERFIL UPDATE PASSWORD: Params - " . json_encode([
                    ':password' => $nueva_contrasena_hash,
                    ':usuario_id' => $usuarioActual['id']
                ]));
                $stmt = $pdo->prepare($sql);
                $stmt->execute([
                    ':password' => $nueva_contrasena_hash,
                    ':usuario_id' => $usuarioActual['id']
                ]);

                $pdo->commit();
                echo json_encode(['success' => true, 'message' => 'Contraseña cambiada correctamente']);
                break;

            case 'avatar':
                // Actualizar foto de perfil
                if (!isset($_FILES['foto_perfil']) || $_FILES['foto_perfil']['error'] !== UPLOAD_ERR_OK) {
                    throw new Exception('Error al subir el archivo.');
                }

                $file = $_FILES['foto_perfil'];
                $fileTmpName = $file['tmp_name'];
                $fileSize = $file['size'];
                $fileType = $file['type'];

                $allowed = array('jpg' => 'image/jpeg', 'jpeg' => 'image/jpeg', 'png' => 'image/png', 'gif' => 'image/gif');
                $fileExt = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));

                if (!array_key_exists($fileExt, $allowed) || !in_array($fileType, $allowed)) {
                    throw new Exception('Tipo de archivo no permitido. Solo JPG, JPEG, PNG, GIF.');
                }
                
                if ($fileSize > 5000000) { // 5MB
                    throw new Exception('Tu archivo es demasiado grande (máx. 5MB).');
                }

                // Leer el contenido del archivo en binario
                $imagenBinaria = file_get_contents($fileTmpName);

                if ($imagenBinaria === false) {
                    throw new Exception('No se pudo leer el archivo de imagen.');
                }

                $pdo->beginTransaction();

                // Obtener persona_id
                $sqlPersona = "SELECT id FROM personas WHERE usuario_id = :usuario_id";
                $stmtPersona = $pdo->prepare($sqlPersona);
                $stmtPersona->execute([':usuario_id' => $usuarioActual['id']]);
                $persona = $stmtPersona->fetch();

                if (!$persona) {
                    throw new Exception('No se encontró información de persona para actualizar el avatar.');
                }

                $sql = "UPDATE personas SET
                            foto_perfil = :foto_perfil,
                            updated_at = CURRENT_TIMESTAMP
                        WHERE id = :persona_id";

                $stmt = $pdo->prepare($sql);
                // Usar PDO::PARAM_LOB para datos binarios grandes
                $stmt->bindParam(':foto_perfil', $imagenBinaria, PDO::PARAM_LOB);
                $stmt->bindParam(':persona_id', $persona['id']);
                $stmt->execute();

                $pdo->commit();

                // Actualizar la sesión con la nueva foto de perfil (codificada en base64 para mostrarla)
                $_SESSION['foto_perfil'] = 'data:' . $fileType . ';base64,' . base64_encode($imagenBinaria);

                echo json_encode(['success' => true, 'message' => 'Foto de perfil actualizada correctamente', 'new_image_url' => $_SESSION['foto_perfil']]);
                break;

            default:
                throw new Exception('Tipo de actualización no válido');
        }

    } catch (Exception $e) {
        if (isset($pdo) && $pdo->inTransaction()) {
            $pdo->rollBack();
        }
        error_log("PERFIL UPDATE ERROR: " . $e->getMessage());
        error_log("PERFIL UPDATE ERROR TRACE: " . $e->getTraceAsString());
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
    exit;
}

// Obtener datos del usuario actual
$usuarioActual = AuthController::obtenerUsuarioActual();
$nombreCompleto = trim($usuarioActual['nombres'] . ' ' . $usuarioActual['apellido_paterno'] . ' ' . $usuarioActual['apellido_materno']);

// Obtener la foto de perfil desde la base de datos
$pdo = Conexion::getConexion();
$sqlFoto = "SELECT foto_perfil FROM personas WHERE usuario_id = :usuario_id";
$stmtFoto = $pdo->prepare($sqlFoto);
$stmtFoto->execute([':usuario_id' => $usuarioActual['id']]);
$resultadoFoto = $stmtFoto->fetch(PDO::FETCH_ASSOC);

$fotoPerfilUrl = null;
if ($resultadoFoto && !empty($resultadoFoto['foto_perfil'])) {
    // Asumiendo que la imagen está en formato binario, la codificamos en Base64
    $fotoPerfilUrl = 'data:image/jpeg;base64,' . base64_encode($resultadoFoto['foto_perfil']);
}

// Generar iniciales para avatar por defecto
$iniciales = '';
if (!empty($usuarioActual['nombres'])) {
    $iniciales .= substr($usuarioActual['nombres'], 0, 1);
}
if (!empty($usuarioActual['apellido_paterno'])) {
    $iniciales .= substr($usuarioActual['apellido_paterno'], 0, 1);
}

// Obtener información completa del administrador desde la base de datos
$administrador = new Administrador();
$datosCompletos = $administrador->obtenerPorUsuarioId($usuarioActual['id']);

// Obtener contacto de emergencia si existe
$pdo = Conexion::getConexion();
$sqlContacto = "SELECT ce.*, tce.nombre as tipo_contacto_nombre
                FROM contactos_emergencia ce
                LEFT JOIN tipos_contacto_emergencia tce ON ce.tipo_contacto_id = tce.id
                WHERE ce.persona_id = :persona_id
                LIMIT 1";
$stmtContacto = $pdo->prepare($sqlContacto);
$stmtContacto->bindParam(':persona_id', $datosCompletos['persona_id']);
$stmtContacto->execute();
$contactoEmergencia = $stmtContacto->fetch();

// Calcular edad si existe fecha de nacimiento
$edad = null;
if (!empty($datosCompletos['fecha_nacimiento'])) {
    $fechaNacimiento = new DateTime($datosCompletos['fecha_nacimiento']);
    $hoy = new DateTime();
    $edad = $hoy->diff($fechaNacimiento)->y;
}

// Formatear fechas para mostrar
$fechaNacimientoFormateada = null;
$fechaContratacionFormateada = null;

if (!empty($datosCompletos['fecha_nacimiento'])) {
    $fechaNacimientoFormateada = date('d/m/Y', strtotime($datosCompletos['fecha_nacimiento']));
}

if (!empty($datosCompletos['fecha_contratacion'])) {
    $fechaContratacionFormateada = date('d/m/Y', strtotime($datosCompletos['fecha_contratacion']));
}

// Formatear sexo para mostrar
$sexoFormateado = '';
if (!empty($datosCompletos['sexo'])) {
    $sexoFormateado = ($datosCompletos['sexo'] === 'masculino') ? 'Masculino' : 'Femenino';
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Plataforma Educativa - Perfil Administrador</title>
  <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap">
  <link rel="stylesheet" href="./Css/plataforma.css">
  <link rel="stylesheet" href="./Css/admin.css">
  <link rel="stylesheet" href="./Css/perfil_a.css">
  <link rel="stylesheet" href="./Css/avatar-default.css">
</head>
<body>
  <div class="plataforma-container">
      <!-- Menú lateral -->
      <aside class="sidebar">
          <div class="sidebar-header">
              <div class="logo">
                  <img src="./img/logo-escuela.svg" alt="Logo Escuela">
              </div>
              <button class="menu-toggle" id="menu-toggle">
                  <span class="material-icons">menu</span>
              </button>
          </div>
          
          <div class="sidebar-content">
              <div class="user-info">
                  <div class="user-avatar <?php echo $fotoPerfilUrl ? '' : 'default-avatar admin'; ?>" <?php echo $fotoPerfilUrl ? '' : 'data-initials="' . htmlspecialchars($iniciales) . '"'; ?>>
                      <?php if ($fotoPerfilUrl): ?>
                          <img src="<?php echo htmlspecialchars($fotoPerfilUrl); ?>" alt="Foto de perfil">
                      <?php endif; ?>
                  </div>
                  <div class="user-details">
                      <h3><?php echo htmlspecialchars($nombreCompleto); ?></h3>
                      <p>Administrador</p>
                  </div>
              </div>
              
              <nav class="sidebar-menu">
                  <ul>
                      <li>
                          <a href="inicio_a.php">
                              <span class="material-icons">dashboard</span>
                              <span>Inicio</span>
                          </a>
                      </li>
                      <li class="active">
                          <a href="perfil_a.php">
                              <span class="material-icons">person</span>
                              <span>Perfil</span>
                          </a>
                      </li>
                      <li>
                          <a href="usuarios_a.html">
                              <span class="material-icons">people</span>
                              <span>Usuarios</span>
                          </a>
                      </li>
                      <li>
                          <a href="crear_anuncio.php">
                              <span class="material-icons">campaign</span>
                              <span>Anuncios</span>
                          </a>
                      </li>
                      <li>
                          <a href="admision_a.php">
                              <span class="material-icons">how_to_reg</span>
                              <span>Solicitudes de Admisión</span>
                          </a>
                      </li>
                      <li>
                          <a href="configuracion_a.php">
                              <span class="material-icons">settings</span>
                              <span>Configuración</span>
                          </a>
                      </li>
                      <li class="separator"></li>
                      <li>
                          <a href="../Controlador/AuthController.php?action=logout">
                              <span class="material-icons">logout</span>
                              <span>Cerrar Sesión</span>
                          </a>
                      </li>
                  </ul>
              </nav>
          </div>
      </aside>
      
      <!-- Contenido principal -->
      <main class="main-content">
          <header class="content-header">
              <div class="header-left">
                  <h1>Mi Perfil</h1>
                  <p class="current-date">Lunes, 22 de marzo de 2025</p>
              </div>
              <div class="header-right">
                  <div class="notifications">
                      <button class="notification-btn">
                          <span class="material-icons">notifications</span>
                          <span class="notification-badge">5</span>
                      </button>
                  </div>
              </div>
          </header>
          
          <div class="content-body">
              <div class="profile-container">
                  <!-- Sección de perfil principal -->
                  <section class="profile-main">
                      <div class="profile-header">
                          <div class="profile-avatar-container">
                              <div class="profile-avatar <?php echo $fotoPerfilUrl ? '' : 'default-avatar admin'; ?>" <?php echo $fotoPerfilUrl ? '' : 'data-initials="' . htmlspecialchars($iniciales) . '"'; ?>>
                                  <?php if ($fotoPerfilUrl): ?>
                                      <img src="<?php echo htmlspecialchars($fotoPerfilUrl); ?>" alt="Foto de perfil">
                                  <?php endif; ?>
                              </div>
                              <button class="change-avatar-btn">
                                  <span class="material-icons">photo_camera</span>
                                  <span>Cambiar foto</span>
                              </button>
                          </div>
                          <div class="profile-info">
                              <h2><?php echo htmlspecialchars($nombreCompleto); ?></h2>
                              <p class="profile-role"><?php echo htmlspecialchars($datosCompletos['cargo'] ?? 'Administrador del Sistema'); ?></p>
                              <p class="profile-grade"><?php echo htmlspecialchars($datosCompletos['departamento'] ?? 'Acceso Total'); ?></p>
                          </div>
                      </div>
                  </section>
                  
                  <!-- Sección de información personal -->
                  <section class="profile-section">
                      <div class="section-header">
                          <h3>
                              <span class="material-icons">person</span>
                              Información Personal
                          </h3>
                          <button class="edit-section-btn" data-section="personal">
                              <span class="material-icons">edit</span>
                              Editar
                          </button>
                      </div>
                      
                      <div class="section-content">
                          <div class="profile-info-grid">
                              <div class="info-item">
                                  <div class="info-label">Nombre completo</div>
                                  <div class="info-value"><?php echo htmlspecialchars($nombreCompleto); ?></div>
                              </div>
                              <div class="info-item">
                                  <div class="info-label">DNI</div>
                                  <div class="info-value"><?php echo htmlspecialchars($datosCompletos['dni'] ?? 'No registrado'); ?></div>
                              </div>
                              <div class="info-item">
                                  <div class="info-label">Fecha de nacimiento</div>
                                  <div class="info-value"><?php echo htmlspecialchars($fechaNacimientoFormateada ?? 'No registrada'); ?></div>
                              </div>
                              <div class="info-item">
                                  <div class="info-label">Edad</div>
                                  <div class="info-value"><?php echo $edad ? $edad . ' años' : 'No calculada'; ?></div>
                              </div>
                              <div class="info-item">
                                  <div class="info-label">Sexo</div>
                                  <div class="info-value"><?php echo htmlspecialchars($sexoFormateado ?: 'No especificado'); ?></div>
                              </div>
                              <div class="info-item">
                                  <div class="info-label">Dirección</div>
                                  <div class="info-value"><?php echo htmlspecialchars($datosCompletos['direccion'] ?? 'No registrada'); ?></div>
                              </div>
                              <div class="info-item">
                                  <div class="info-label">Teléfono</div>
                                  <div class="info-value"><?php echo htmlspecialchars($datosCompletos['telefono'] ?? 'No registrado'); ?></div>
                              </div>
                          </div>
                      </div>
                  </section>
                  
                  <!-- Sección de cuenta -->
                  <section class="profile-section">
                      <div class="section-header">
                          <h3>
                              <span class="material-icons">account_circle</span>
                              Información de Cuenta
                          </h3>
                          <button class="edit-section-btn" data-section="account">
                              <span class="material-icons">edit</span>
                              Editar
                          </button>
                      </div>
                      
                      <div class="section-content">
                          <div class="profile-info-grid">
                              <div class="info-item">
                                  <div class="info-label">Nombre de usuario</div>
                                  <div class="info-value"><?php echo htmlspecialchars($usuarioActual['nombre_usuario']); ?></div>
                              </div>
                              <div class="info-item">
                                  <div class="info-label">Correo electrónico</div>
                                  <div class="info-value"><?php echo htmlspecialchars($usuarioActual['email']); ?></div>
                              </div>
                              <div class="info-item">
                                  <div class="info-label">Contraseña</div>
                                  <div class="info-value password-field">••••••••••
                                      <button class="change-password-btn">
                                          <span class="material-icons">lock</span>
                                          Cambiar
                                      </button>
                                  </div>
                              </div>
                          </div>
                      </div>
                  </section>
                  
                  <!-- Sección profesional -->
                  <section class="profile-section">
                      <div class="section-header">
                          <h3>
                              <span class="material-icons">work</span>
                              Información Profesional
                          </h3>
                      </div>
                      
                      <div class="section-content">
                          <div class="profile-info-grid">
                              <div class="info-item">
                                  <div class="info-label">Cargo</div>
                                  <div class="info-value"><?php echo htmlspecialchars($datosCompletos['cargo'] ?? 'No especificado'); ?></div>
                              </div>
                              <div class="info-item">
                                  <div class="info-label">Departamento</div>
                                  <div class="info-value"><?php echo htmlspecialchars($datosCompletos['departamento'] ?? 'No especificado'); ?></div>
                              </div>
                              <div class="info-item">
                                  <div class="info-label">Fecha de contratación</div>
                                  <div class="info-value"><?php echo htmlspecialchars($fechaContratacionFormateada ?? 'No registrada'); ?></div>
                              </div>
                          </div>
                      </div>
                  </section>

                  
                  <!-- Sección de contacto de emergencia -->
                  <section class="profile-section">
                      <div class="section-header">
                          <h3>
                              <span class="material-icons">contact_phone</span>
                              Contacto de Emergencia
                          </h3>
                          <button class="edit-section-btn" data-section="emergency">
                              <span class="material-icons">edit</span>
                              Editar
                          </button>
                      </div>
                      
                      <div class="section-content">
                          <?php if ($contactoEmergencia): ?>
                          <div class="profile-info-grid">
                              <div class="info-item">
                                  <div class="info-label">Nombre del contacto</div>
                                  <div class="info-value"><?php echo htmlspecialchars($contactoEmergencia['nombre_contacto']); ?></div>
                              </div>
                              <div class="info-item">
                                  <div class="info-label">Teléfono principal</div>
                                  <div class="info-value"><?php echo htmlspecialchars($contactoEmergencia['telefono_principal']); ?></div>
                              </div>
                              <div class="info-item">
                                  <div class="info-label">Teléfono alternativo</div>
                                  <div class="info-value"><?php echo htmlspecialchars($contactoEmergencia['telefono_alternativo'] ?? 'No registrado'); ?></div>
                              </div>
                              <div class="info-item">
                                  <div class="info-label">Correo electrónico</div>
                                  <div class="info-value"><?php echo htmlspecialchars($contactoEmergencia['email'] ?? 'No registrado'); ?></div>
                              </div>
                          </div>
                          <?php else: ?>
                          <div class="no-data-message">
                              <p>No hay contacto de emergencia registrado.</p>
                              <button class="btn-primary add-emergency-contact-btn">
                                  <span class="material-icons">add</span>
                                  Agregar contacto de emergencia
                              </button>
                          </div>
                          <?php endif; ?>
                      </div>
                  </section>
              </div>
          </div>
      </main>
  </div>
  
  <!-- Modal para editar información personal -->
  <div id="edit-personal-modal" class="modal-overlay">
      <div class="modal-content">
          <div class="modal-header">
              <h3>Editar Información Personal</h3>
              <button class="modal-close-btn">
                  <span class="material-icons">close</span>
              </button>
          </div>
          <div class="modal-body">
              <form class="edit-form" method="POST">
                  <div class="form-row">
                      <div class="form-group">
                          <label for="nombres">Nombres</label>
                          <input type="text" id="nombres" name="nombres" value="<?php echo htmlspecialchars($datosCompletos['nombres'] ?? ''); ?>" required>
                      </div>
                      <div class="form-group">
                          <label for="apellido_paterno">Apellido paterno</label>
                          <input type="text" id="apellido_paterno" name="apellido_paterno" value="<?php echo htmlspecialchars($datosCompletos['apellido_paterno'] ?? ''); ?>" required>
                      </div>
                  </div>
                  <div class="form-row">
                      <div class="form-group">
                          <label for="apellido_materno">Apellido materno</label>
                          <input type="text" id="apellido_materno" name="apellido_materno" value="<?php echo htmlspecialchars($datosCompletos['apellido_materno'] ?? ''); ?>" required>
                      </div>
                      <div class="form-group">
                          <label for="dni">DNI</label>
                          <input type="text" id="dni" name="dni" value="<?php echo htmlspecialchars($datosCompletos['dni'] ?? ''); ?>" maxlength="8">
                      </div>
                  </div>
                  <div class="form-row">
                      <div class="form-group">
                          <label for="birthdate">Fecha de nacimiento</label>
                          <input type="date" id="birthdate" name="fecha_nacimiento" value="<?php echo htmlspecialchars($datosCompletos['fecha_nacimiento'] ?? ''); ?>">
                      </div>
                      <div class="form-group">
                          <label for="gender">Sexo</label>
                          <select id="gender" name="sexo" required>
                              <option value="">Seleccionar...</option>
                              <option value="masculino" <?php echo ($datosCompletos['sexo'] ?? '') === 'masculino' ? 'selected' : ''; ?>>Masculino</option>
                              <option value="femenino" <?php echo ($datosCompletos['sexo'] ?? '') === 'femenino' ? 'selected' : ''; ?>>Femenino</option>
                          </select>
                      </div>
                  </div>
                  <div class="form-group">
                      <label for="address">Dirección</label>
                      <input type="text" id="address" name="direccion" value="<?php echo htmlspecialchars($datosCompletos['direccion'] ?? ''); ?>">
                  </div>
                  <div class="form-group">
                      <label for="phone">Teléfono</label>
                      <input type="tel" id="phone" name="telefono" value="<?php echo htmlspecialchars($datosCompletos['telefono'] ?? ''); ?>">
                  </div>

                  <div class="form-actions">
                      <button type="button" class="btn-secondary modal-close-btn">Cancelar</button>
                      <button type="submit" class="btn-primary">Guardar cambios</button>
                  </div>
              </form>
          </div>
      </div>
  </div>
  
  <!-- Modal para editar información de cuenta -->
  <div id="edit-account-modal" class="modal-overlay">
      <div class="modal-content">
          <div class="modal-header">
              <h3>Editar Información de Cuenta</h3>
              <button class="modal-close-btn">
                  <span class="material-icons">close</span>
              </button>
          </div>
          <div class="modal-body">
              <form class="edit-form" method="POST">
                  <div class="form-group">
                      <label for="username">Nombre de usuario</label>
                      <input type="text" id="username" name="nombre_usuario" value="<?php echo htmlspecialchars($usuarioActual['nombre_usuario']); ?>" required minlength="3" maxlength="50">
                  </div>
                  <div class="form-group">
                      <label for="email">Correo electrónico</label>
                      <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($usuarioActual['email']); ?>" required>
                  </div>

                  <div class="form-actions">
                      <button type="button" class="btn-secondary modal-close-btn">Cancelar</button>
                      <button type="submit" class="btn-primary">Guardar cambios</button>
                  </div>
              </form>
          </div>
      </div>
  </div>
  
  <!-- Modal para cambiar contraseña -->
  <div id="change-password-modal" class="modal-overlay">
      <div class="modal-content">
          <div class="modal-header">
              <h3>Cambiar Contraseña</h3>
              <button class="modal-close-btn">
                  <span class="material-icons">close</span>
              </button>
          </div>
          <div class="modal-body">
              <form class="edit-form" method="POST">
                  <div class="form-group">
                      <label for="current-password">Contraseña actual</label>
                      <input type="password" id="current-password" name="contrasena_actual" required>
                  </div>
                  <div class="form-group">
                      <label for="new-password">Nueva contraseña</label>
                      <input type="password" id="new-password" name="nueva_contrasena" required minlength="8">
                  </div>
                  <div class="form-group">
                      <label for="confirm-password">Confirmar nueva contraseña</label>
                      <input type="password" id="confirm-password" name="confirmar_contrasena" required minlength="8">
                  </div>

                  <div class="password-requirements">
                      <p>La contraseña debe cumplir con los siguientes requisitos:</p>
                      <ul>
                          <li>Mínimo 8 caracteres</li>
                          <li>Al menos una letra mayúscula</li>
                          <li>Al menos un número</li>
                          <li>Al menos un carácter especial</li>
                      </ul>
                  </div>

                  <div class="form-actions">
                      <button type="button" class="btn-secondary modal-close-btn">Cancelar</button>
                      <button type="submit" class="btn-primary">Cambiar contraseña</button>
                  </div>
              </form>
          </div>
      </div>
  </div>
  
  <!-- Modal para editar información profesional -->
  <div id="edit-professional-modal" class="modal-overlay">
      <div class="modal-content">
          <div class="modal-header">
              <h3>Editar Información Profesional</h3>
              <button class="modal-close-btn">
                  <span class="material-icons">close</span>
              </button>
          </div>
          <div class="modal-body">
              <form class="edit-form" method="POST">
                  <div class="form-group">
                      <label for="position">Cargo</label>
                      <input type="text" id="position" name="cargo" value="<?php echo htmlspecialchars($datosCompletos['cargo'] ?? ''); ?>" required maxlength="100">
                  </div>
                  <div class="form-group">
                      <label for="department">Departamento</label>
                      <input type="text" id="department" name="departamento" value="<?php echo htmlspecialchars($datosCompletos['departamento'] ?? ''); ?>" maxlength="100">
                  </div>
                  <div class="form-group">
                      <label for="hire-date">Fecha de contratación</label>
                      <input type="date" id="hire-date" name="fecha_contratacion" value="<?php echo htmlspecialchars($datosCompletos['fecha_contratacion'] ?? ''); ?>">
                  </div>

                  <div class="form-actions">
                      <button type="button" class="btn-secondary modal-close-btn">Cancelar</button>
                      <button type="submit" class="btn-primary">Guardar cambios</button>
                  </div>
              </form>
          </div>
      </div>
  </div>

  
  <!-- Modal para editar contacto de emergencia -->
  <div id="edit-emergency-modal" class="modal-overlay">
      <div class="modal-content">
          <div class="modal-header">
              <h3>Editar Contacto de Emergencia</h3>
              <button class="modal-close-btn">
                  <span class="material-icons">close</span>
              </button>
          </div>
          <div class="modal-body">
              <form class="edit-form" method="POST">
                  <div class="form-group">
                      <label for="emergency-name">Nombre del contacto</label>
                      <input type="text" id="emergency-name" name="nombre_contacto" value="<?php echo htmlspecialchars($contactoEmergencia['nombre_contacto'] ?? ''); ?>" required maxlength="100">
                  </div>
                  <div class="form-group">
                      <label for="emergency-phone">Teléfono principal</label>
                      <input type="tel" id="emergency-phone" name="telefono_principal" value="<?php echo htmlspecialchars($contactoEmergencia['telefono_principal'] ?? ''); ?>" required>
                  </div>
                  <div class="form-group">
                      <label for="emergency-alt-phone">Teléfono alternativo</label>
                      <input type="tel" id="emergency-alt-phone" name="telefono_alternativo" value="<?php echo htmlspecialchars($contactoEmergencia['telefono_alternativo'] ?? ''); ?>">
                  </div>
                  <div class="form-group">
                      <label for="emergency-email">Correo electrónico</label>
                      <input type="email" id="emergency-email" name="email" value="<?php echo htmlspecialchars($contactoEmergencia['email'] ?? ''); ?>">
                  </div>

                  <div class="form-actions">
                      <button type="button" class="btn-secondary modal-close-btn">Cancelar</button>
                      <button type="submit" class="btn-primary">Guardar cambios</button>
                  </div>
              </form>
          </div>
      </div>
  </div>

  <script src="./Js/plataforma.js"></script>
  <script src="./Js/perfil_a.js"></script>
</body>
</html>
