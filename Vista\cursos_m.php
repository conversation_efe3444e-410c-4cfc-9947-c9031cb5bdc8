<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once '../Controlador/AuthController.php';
require_once '../Modelo/Maestro.php';
require_once '../Modelo/Curso.php';

AuthController::proteger<PERSON><PERSON><PERSON>(['maestro']);
$usuarioActual = AuthController::obtenerUsuarioActual();
$maestroModel = new Maestro();
$datosMaestro = $maestroModel->obtenerPorUsuarioId($usuarioActual['id']);
if (!$datosMaestro) die("Error: No se pudo cargar la información del maestro.");

$cursoModel = new Curso();
$cursos = $cursoModel->obtenerCursosPorMaestroId($datosMaestro['id']);
$nombreCompleto = trim($usuarioActual['nombres'] . ' ' . $usuarioActual['apellido_paterno']);
$fotoPerfilUrl = !empty($usuarioActual['foto_perfil']) ? $usuarioActual['foto_perfil'] : null;
$iniciales = strtoupper(substr($usuarioActual['nombres'], 0, 1) . substr($usuarioActual['apellido_paterno'], 0, 1));
$especialidad = $datosMaestro['especialidad'] ?? 'No especificada';
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mis Cursos - Plataforma Educativa</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap">
    <link rel="stylesheet" href="./Css/plataforma.css">
    <link rel="stylesheet" href="./Css/cursos_m.css">
</head>
<body>
    <div class="plataforma-container">
        <!-- Menú lateral (Sidebar) -->
        <aside class="sidebar">
            <div class="sidebar-header"><div class="logo"><img src="./img/logo-escuela.svg" alt="Logo"></div></div>
            <div class="sidebar-content">
                <div class="user-info">
                    <div class="user-avatar <?php echo $fotoPerfilUrl ? '' : 'default-avatar maestro'; ?>" data-initials="<?php echo htmlspecialchars($iniciales); ?>">
                        <?php if ($fotoPerfilUrl) echo "<img src='".htmlspecialchars($fotoPerfilUrl)."' alt='Foto'>"; ?>
                    </div>
                    <div class="user-details">
                        <h3><?php echo htmlspecialchars($nombreCompleto); ?></h3><p>Profesor</p>
                    </div>
                </div>
                <nav class="sidebar-menu">
                    <ul>
                        <li><a href="inicio_m.php"><span class="material-icons">home</span><span>Inicio</span></a></li>
                        <li><a href="perfil_m.php"><span class="material-icons">person</span><span>Perfil</span></a></li>
                        <li class="active"><a href="cursos_m.php"><span class="material-icons">school</span><span>Mis Cursos</span></a></li>
                        <li><a href="asistencia_m.php"><span class="material-icons">fact_check</span><span>Asistencia</span></a></li>
                        <li><a href="mensajes_mp.php"><span class="material-icons">chat</span><span>Mensajes</span></a></li>
                        <li class="separator"></li>
                        <li><a href="../Controlador/AuthController.php?action=logout"><span class="material-icons">logout</span><span>Cerrar Sesión</span></a></li>
                    </ul>
                </nav>
            </div>
        </aside>
        
        <!-- Contenido Principal -->
        <main class="main-content">
            <header class="content-header">
                <div class="header-left">
                    <h1>Mis Cursos</h1>
                    <p class="current-date"></p>
                </div>
                 <div class="header-right">
                    <button id="create-course-btn" class="create-course-btn"><span class="material-icons">add</span>Crear Curso</button>
                </div>
            </header>
            
            <div class="content-body">
                <section class="courses-actions">
                    <div class="filter-container">
                        <div class="search-box">
                            <span class="material-icons">search</span>
                            <input type="text" id="search-course-input" placeholder="Buscar cursos...">
                        </div>
                    </div>
                </section>
                
                <section class="courses-grid">
                    <?php if (!empty($cursos)): foreach ($cursos as $curso): ?>
                        <div class="course-card" data-course-name="<?php echo htmlspecialchars(strtolower($curso['nombre'])); ?>">
                            <div class="course-card-header">
                                <img src="<?php echo htmlspecialchars(!empty($curso['imagen']) ? $curso['imagen'] : './img/curso_default.png'); ?>" alt="<?php echo htmlspecialchars($curso['nombre']); ?>">
                                <div class="course-icon"><span class="material-icons"><?php echo htmlspecialchars($curso['icono'] ?? 'book'); ?></span></div>
                                <div class="course-actions">
                                    <button class="course-action-btn edit-btn" data-id="<?php echo $curso['id']; ?>"><span class="material-icons">edit</span></button>
                                    <button class="course-action-btn delete-btn" data-id="<?php echo $curso['id']; ?>"><span class="material-icons">delete</span></button>
                                </div>
                            </div>
                            <div class="course-card-body">
                                <h3><?php echo htmlspecialchars($curso['nombre']); ?></h3>
                                <p class="course-grade"><?php echo htmlspecialchars($curso['grado']); ?>° Primaria</p>
                                
                                <!-- ================================== -->
                                <!-- INICIO: Horario del Curso          -->
                                <!-- ================================== -->
                                <?php if (!empty($curso['horarios'])): ?>
                                <div class="course-schedule">
                                    <?php foreach ($curso['horarios'] as $horario): ?>
                                    <div class="schedule-item">
                                        <span class="material-icons">schedule</span>
                                        <span>
                                            <?php echo htmlspecialchars(ucfirst($horario['dia_semana'])); ?> 
                                            de <?php echo date("H:i", strtotime($horario['hora_inicio'])); ?> 
                                            a <?php echo date("H:i", strtotime($horario['hora_fin'])); ?>
                                        </span>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                                <?php endif; ?>
                                <!-- ================================== -->
                                <!-- FIN: Horario del Curso             -->
                                <!-- ================================== -->
                                
                                <div class="course-details">
                                    <div class="course-detail">
                                        <span class="material-icons">groups</span>
                                        <span>0 estudiantes</span>
                                    </div>
                                </div>
                            </div>
                            <div class="course-card-footer">
                                <a href="#" class="view-course-btn"><span class="material-icons">visibility</span> Ver curso</a>
                            </div>
                        </div>
                    <?php endforeach; else: ?>
                         <p>No tienes cursos creados. Haz clic en "Crear Curso" para empezar.</p>
                    <?php endif; ?>
                </section>
            </div>
        </main>
    </div>
    
    <!-- Modales -->
    <div id="course-modal" class="modal-overlay">
        <div class="modal-content">
            <form id="course-form" method="POST" enctype="multipart/form-data" novalidate>
                <div class="modal-header">
                    <h3 id="modal-title"></h3>
                    <button type="button" class="modal-close-btn"><span class="material-icons">close</span></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="course-id" name="id">
                    <input type="hidden" name="especialidad" value="<?php echo htmlspecialchars($datosMaestro['especialidad']); ?>">
                    <div class="form-group">
                        <label for="course-name">Nombre del Curso</label>
                        <input type="text" id="course-name" name="nombre" required>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="course-grade">Grado</label>
                            <select id="course-grade" name="grado" required>
                                <option value="" disabled selected>Seleccionar grado</option>
                                <?php for ($i=1; $i<=6; $i++) echo "<option value='{$i}'>{$i}° Primaria</option>"; ?>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="course-year">Año Escolar</label>
                            <input type="number" id="course-year" name="anio_escolar" value="<?php echo date('Y'); ?>" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Horario</label>
                        <div class="schedule-inputs">
                            <?php foreach (['lunes', 'martes', 'miercoles', 'jueves', 'viernes'] as $dia): ?>
                            <div class="schedule-day">
                                <label><input type="checkbox" name="schedule-day[]" value="<?php echo $dia; ?>"> <?php echo ucfirst($dia); ?></label>
                                <div class="time-inputs">
                                    <input type="time" name="start-time-<?php echo $dia; ?>" disabled>
                                    <span>a</span>
                                    <input type="time" name="end-time-<?php echo $dia; ?>" disabled>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="course-icon">Ícono del Curso</label>
                        <select id="course-icon" name="icono" required>
                            <option value="calculate" selected>Matemáticas</option><option value="science">Ciencia</option><option value="menu_book">Comunicación</option>
                            <option value="public">Personal Social</option><option value="brush">Arte</option><option value="directions_run">Ed. Física</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="course-image">Imagen del Curso (opcional)</label>
                        <div class="file-upload">
                            <input type="file" id="course-image" name="imagen" accept="image/*">
                            <div class="file-upload-btn"><span class="material-icons">image</span><span id="file-name-display">Seleccionar imagen</span></div>
                        </div>
                    </div>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn-secondary modal-close-btn">Cancelar</button>
                    <button type="submit" class="btn-primary">Guardar Curso</button>
                </div>
            </form>
        </div>
    </div>
    
    <div id="delete-modal" class="modal-overlay">
        <div class="modal-content modal-small">
            <div class="modal-header"><h3>Eliminar Curso</h3><button type="button" class="modal-close-btn"><span class="material-icons">close</span></button></div>
            <div class="modal-body"><p>¿Está seguro de eliminar este curso? Esta acción no se puede deshacer.</p></div>
            <div class="form-actions">
                <button type="button" class="btn-secondary modal-close-btn">Cancelar</button>
                <button type="button" class="btn-danger" id="confirm-delete-btn">Eliminar</button>
            </div>
        </div>
    </div>

    <script src="./Js/plataforma.js"></script>
    <script src="./Js/cursos_m.js"></script>
</body>
</html>