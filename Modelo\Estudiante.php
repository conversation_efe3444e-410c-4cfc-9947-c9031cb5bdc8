<?php
require_once 'Conexion.php';
require_once 'Usuario.php';

/**
 * Modelo para manejar estudiantes
 */
class Estudiante {
    private $pdo;
    private $usuario;

    public function __construct() {
        $this->pdo = Conexion::getConexion();
        $this->usuario = new Usuario();
    }

    /**
     * Crea un nuevo estudiante
     * @param array $datosUsuario
     * @param array $datosPersona
     * @param array $datosEstudiante
     * @return int|false
     */
    public function crearEstudiante($datosUsuario, $datosPersona, $datosEstudiante) {
        try {
            $this->pdo->beginTransaction();

            // Crear usuario y persona
            $resultado = $this->usuario->crearUsuario($datosUsuario, $datosPersona);
            if (!$resultado) {
                throw new Exception("Error creando usuario base");
            }

            // Insertar datos específicos del estudiante
            $sql = "INSERT INTO estudiantes (persona_id, grado_actual, anio_escolar, maestro_tutor_id) 
                    VALUES (:persona_id, :grado_actual, :anio_escolar, :maestro_tutor_id)";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->bindParam(':persona_id', $resultado['persona_id']);
            $stmt->bindParam(':grado_actual', $datosEstudiante['grado_actual']);
            $stmt->bindParam(':anio_escolar', $datosEstudiante['anio_escolar']);
            $stmt->bindParam(':maestro_tutor_id', $datosEstudiante['maestro_tutor_id']);
            $stmt->execute();

            $estudianteId = $this->pdo->lastInsertId();

            $this->pdo->commit();
            return $estudianteId;

        } catch (Exception $e) {
            $this->pdo->rollBack();
            error_log("Error creando estudiante: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Obtiene todos los estudiantes
     * @return array
     */
    public function obtenerTodos() {
        try {
            $sql = "SELECT e.*, p.nombres, p.apellido_paterno, p.apellido_materno, p.dni, u.email, u.nombre_usuario
                    FROM estudiantes e
                    INNER JOIN personas p ON e.persona_id = p.id
                    INNER JOIN usuarios u ON p.usuario_id = u.id
                    WHERE u.activo = 1
                    ORDER BY p.apellido_paterno, p.apellido_materno, p.nombres";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute();
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Error obteniendo estudiantes: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Obtiene estudiante por ID
     * @param int $id
     * @return array|false
     */
    public function obtenerPorId($id) {
        try {
            $sql = "SELECT e.*, p.nombres, p.apellido_paterno, p.apellido_materno, p.dni, p.fecha_nacimiento, 
                           p.sexo, p.direccion, p.telefono, p.foto_perfil, u.email, u.nombre_usuario
                    FROM estudiantes e
                    INNER JOIN personas p ON e.persona_id = p.id
                    INNER JOIN usuarios u ON p.usuario_id = u.id
                    WHERE e.id = :id AND u.activo = 1";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->bindParam(':id', $id);
            $stmt->execute();
            
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Error obteniendo estudiante: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Obtiene estudiante por usuario_id
     * @param int $usuarioId
     * @return array|false
     */
    public function obtenerPorUsuarioId($usuarioId) {
        try {
            $sql = "SELECT e.*, p.nombres, p.apellido_paterno, p.apellido_materno, p.dni, p.fecha_nacimiento, 
                           p.sexo, p.direccion, p.telefono, p.foto_perfil, u.email, u.nombre_usuario, p.id as persona_id
                    FROM estudiantes e
                    INNER JOIN personas p ON e.persona_id = p.id
                    INNER JOIN usuarios u ON p.usuario_id = u.id
                    WHERE u.id = :usuario_id AND u.activo = 1";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->bindParam(':usuario_id', $usuarioId);
            $stmt->execute();
            
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Error obteniendo estudiante por usuario: " . $e->getMessage());
            return false;
        }
    }
}
?>
