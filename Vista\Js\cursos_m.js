document.addEventListener("DOMContentLoaded", () => {
    // Referencias a elementos del DOM
    const createCourseBtn = document.getElementById("create-course-btn");
    const courseModal = document.getElementById("course-modal");
    const deleteModal = document.getElementById("delete-modal");
    const courseForm = document.getElementById("course-form");
    const modalTitle = document.getElementById("modal-title");
    const confirmDeleteBtn = document.getElementById("confirm-delete-btn");
    const courseImageInput = document.getElementById('course-image');
    const fileNameDisplay = document.getElementById('file-name-display');
    const searchInput = document.getElementById('search-course-input');

    // Actualizar fecha actual
    const dateElement = document.querySelector(".current-date");
    if (dateElement) {
        const options = { weekday: "long", year: "numeric", month: "long", day: "numeric" };
        dateElement.textContent = new Date().toLocaleDateString("es-ES", options);
    }

    // ----- MANEJO DE MODALES -----
    const openModal = (modal) => modal && modal.classList.add("active");
    const closeModal = () => document.querySelectorAll(".modal-overlay.active").forEach(m => m.classList.remove("active"));
    document.querySelectorAll(".modal-close-btn").forEach(btn => btn.addEventListener("click", closeModal));
    document.querySelectorAll('.modal-overlay').forEach(m => m.addEventListener('click', e => e.target === m && closeModal()));

    if (createCourseBtn) {
        createCourseBtn.addEventListener("click", () => {
            courseForm.reset();
            courseForm.querySelector("#course-id").value = "";
            modalTitle.textContent = "Crear Nuevo Curso";
            if (fileNameDisplay) fileNameDisplay.textContent = "Seleccionar imagen";
            document.querySelectorAll('input[name="schedule-day[]"]').forEach(cb => {
                const dayContainer = cb.closest('.schedule-day');
                dayContainer.querySelectorAll('input[type="time"]').forEach(ti => {
                    ti.disabled = true;
                    ti.required = false;
                });
            });
            openModal(courseModal);
        });
    }

    // ----- MANEJO DE FORMULARIO -----
    if (courseImageInput && fileNameDisplay) {
        courseImageInput.addEventListener('change', () => {
            fileNameDisplay.textContent = courseImageInput.files.length > 0 ? courseImageInput.files[0].name : 'Seleccionar imagen';
        });
    }
    
    document.querySelectorAll('input[name="schedule-day[]"]').forEach(checkbox => {
        checkbox.addEventListener('change', (e) => {
            const timeInputs = e.target.closest('.schedule-day').querySelectorAll('input[type="time"]');
            timeInputs.forEach(input => {
                input.disabled = !e.target.checked;
                input.required = e.target.checked;
                if (!e.target.checked) input.value = '';
            });
        });
    });

    if (courseForm) {
        courseForm.addEventListener("submit", (e) => {
            e.preventDefault();
            const formData = new FormData(courseForm);
            formData.append('tipo_actualizacion', formData.get('id') ? 'editar_curso' : 'crear_curso');

            const scheduleData = {};
            document.querySelectorAll('input[name="schedule-day[]"]:checked').forEach(cb => {
                const day = cb.value;
                const startTime = courseForm.querySelector(`input[name="start-time-${day}"]`).value;
                const endTime = courseForm.querySelector(`input[name="end-time-${day}"]`).value;
                if (startTime && endTime) scheduleData[day] = { start: startTime, end: endTime };
            });
            formData.append('horario', JSON.stringify(scheduleData));

            const submitBtn = courseForm.querySelector('button[type="submit"]');
            submitBtn.disabled = true;
            submitBtn.textContent = 'Guardando...';

            fetch('../Controlador/CursoController.php', { method: 'POST', body: formData })
                .then(res => res.json())
                .then(data => {
                    alert(data.message);
                    if (data.success) location.reload();
                })
                .catch(err => {
                    console.error('Error:', err);
                    alert('Error de conexión con el servidor.');
                })
                .finally(() => {
                    submitBtn.disabled = false;
                    submitBtn.textContent = 'Guardar Curso';
                });
        });
    }

    // ----- ACCIONES DE TARJETAS (DELEGACIÓN DE EVENTOS) -----
    document.querySelector('.courses-grid').addEventListener('click', (e) => {
        const editBtn = e.target.closest('.edit-btn');
        const deleteBtn = e.target.closest('.delete-btn');

        if (editBtn) {
            fetch(`../Controlador/CursoController.php?action=get_course&id=${editBtn.dataset.id}`)
                .then(res => res.json())
                .then(res => {
                    if (res.success) {
                        const curso = res.data;
                        courseForm.reset();
                        modalTitle.textContent = "Editar Curso";
                        Object.keys(curso).forEach(key => {
                            const input = courseForm.querySelector(`#course-${key}`);
                            if (input) input.value = curso[key] || '';
                        });
                        if (fileNameDisplay) fileNameDisplay.textContent = "Seleccionar imagen";

                        document.querySelectorAll('input[name="schedule-day[]"]').forEach(cb => {
                            const day = cb.value;
                            const isScheduled = curso.horarios && curso.horarios[day];
                            cb.checked = isScheduled;
                            const timeInputs = cb.closest('.schedule-day').querySelectorAll('input[type="time"]');
                            timeInputs.forEach(ti => {
                                ti.disabled = !isScheduled;
                                ti.required = isScheduled;
                                if(isScheduled) {
                                    if(ti.name.includes('start')) ti.value = curso.horarios[day].hora_inicio;
                                    if(ti.name.includes('end')) ti.value = curso.horarios[day].hora_fin;
                                }
                            });
                        });
                        openModal(courseModal);
                    } else {
                        alert('Error: ' + res.message);
                    }
                }).catch(err => console.error(err));
        }

        if (deleteBtn) {
            confirmDeleteBtn.dataset.id = deleteBtn.dataset.id;
            openModal(deleteModal);
        }
    });

    if (confirmDeleteBtn) {
        confirmDeleteBtn.addEventListener("click", (e) => {
            const formData = new FormData();
            formData.append('tipo_actualizacion', 'eliminar_curso');
            formData.append('id', e.target.dataset.id);
            
            fetch('../Controlador/CursoController.php', { method: 'POST', body: formData })
                .then(res => res.json())
                .then(data => {
                    alert(data.message);
                    if (data.success) location.reload();
                }).catch(err => console.error(err));
        });
    }
    
    // ----- FILTRO DE BÚSQUEDA -----
    if (searchInput) {
        searchInput.addEventListener('input', e => {
            const searchTerm = e.target.value.toLowerCase().trim();
            document.querySelectorAll('.course-card').forEach(card => {
                const courseName = card.dataset.courseName || '';
                card.style.display = courseName.includes(searchTerm) ? 'flex' : 'none';
            });
        });
    }
});