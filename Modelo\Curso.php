<?php
require_once 'Conexion.php';

class Curso {
    private $pdo;

    public function __construct() {
        $this->pdo = Conexion::getConexion();
    }

    /**
     * Obtiene los cursos de un maestro y adjunta sus respectivos horarios.
     * @param int $maestroId El ID del maestro.
     * @return array Un array de cursos, cada uno con una clave 'horarios'.
     */
    public function obtenerCursosPorMaestroId($maestroId) {
        try {
            // 1. Obtener todos los cursos del maestro
            $sqlCursos = "SELECT c.id, c.nombre, c.especialidad, c.grado, c.anio_escolar, c.descripcion, c.icono, c.imagen
                          FROM cursos c
                          WHERE c.maestro_id = :maestro_id ORDER BY c.created_at DESC";
            $stmtCursos = $this->pdo->prepare($sqlCursos);
            $stmtCursos->bindParam(':maestro_id', $maestroId, PDO::PARAM_INT);
            $stmtCursos->execute();
            $cursos = $stmtCursos->fetchAll(PDO::FETCH_ASSOC);

            if (empty($cursos)) {
                return [];
            }

            // 2. Obtener los IDs de todos los cursos encontrados
            $cursoIds = array_column($cursos, 'id');
            
            // 3. Obtener todos los horarios para esos cursos en una sola consulta
            // Usamos `IN` para eficiencia, evitando el problema N+1
            $placeholders = implode(',', array_fill(0, count($cursoIds), '?'));
            $sqlHorarios = "SELECT curso_id, dia_semana, hora_inicio, hora_fin 
                            FROM horarios 
                            WHERE curso_id IN ($placeholders) ORDER BY FIELD(dia_semana, 'lunes', 'martes', 'miercoles', 'jueves', 'viernes')";
            
            $stmtHorarios = $this->pdo->prepare($sqlHorarios);
            $stmtHorarios->execute($cursoIds);
            $horarios = $stmtHorarios->fetchAll(PDO::FETCH_ASSOC);

            // 4. Organizar los horarios en un array asociativo por curso_id
            $horariosPorCurso = [];
            foreach ($horarios as $horario) {
                $horariosPorCurso[$horario['curso_id']][] = $horario;
            }

            // 5. Adjuntar los horarios a cada curso correspondiente
            foreach ($cursos as &$curso) {
                $curso['horarios'] = $horariosPorCurso[$curso['id']] ?? [];
            }

            return $cursos;

        } catch (PDOException $e) {
            error_log("Error obteniendo cursos y horarios: " . $e->getMessage());
            return [];
        }
    }

    public function crearCurso($datosCurso) {
        try {
            $sql = "INSERT INTO cursos (nombre, especialidad, maestro_id, grado, anio_escolar, descripcion, icono, imagen)
                    VALUES (:nombre, :especialidad, :maestro_id, :grado, :anio_escolar, :descripcion, :icono, :imagen)";
            $stmt = $this->pdo->prepare($sql);
            $stmt->bindParam(':nombre', $datosCurso['nombre']);
            $stmt->bindParam(':especialidad', $datosCurso['especialidad']);
            $stmt->bindParam(':maestro_id', $datosCurso['maestro_id'], PDO::PARAM_INT);
            $stmt->bindParam(':grado', $datosCurso['grado']);
            $stmt->bindParam(':anio_escolar', $datosCurso['anio_escolar'], PDO::PARAM_INT);
            $stmt->bindParam(':descripcion', $datosCurso['descripcion']);
            $stmt->bindParam(':icono', $datosCurso['icono']);
            $stmt->bindParam(':imagen', $datosCurso['imagen']);
            $stmt->execute();
            return $this->pdo->lastInsertId();
        } catch (PDOException $e) {
            error_log("Error creando curso: " . $e->getMessage());
            return false;
        }
    }

    public function actualizarCurso($cursoId, $maestroId, $datosCurso) {
        try {
            $sql = "UPDATE cursos SET
                        nombre = :nombre, especialidad = :especialidad, grado = :grado,
                        anio_escolar = :anio_escolar, descripcion = :descripcion,
                        icono = :icono, imagen = :imagen, updated_at = CURRENT_TIMESTAMP
                    WHERE id = :id AND maestro_id = :maestro_id";
            $stmt = $this->pdo->prepare($sql);
            $stmt->bindParam(':nombre', $datosCurso['nombre']);
            $stmt->bindParam(':especialidad', $datosCurso['especialidad']);
            $stmt->bindParam(':grado', $datosCurso['grado']);
            $stmt->bindParam(':anio_escolar', $datosCurso['anio_escolar'], PDO::PARAM_INT);
            $stmt->bindParam(':descripcion', $datosCurso['descripcion']);
            $stmt->bindParam(':icono', $datosCurso['icono']);
            $stmt->bindParam(':imagen', $datosCurso['imagen']);
            $stmt->bindParam(':id', $cursoId, PDO::PARAM_INT);
            $stmt->bindParam(':maestro_id', $maestroId, PDO::PARAM_INT);
            $stmt->execute();
            return $stmt->rowCount() > 0;
        } catch (PDOException $e) {
            error_log("Error actualizando curso: " . $e->getMessage());
            return false;
        }
    }

    public function eliminarCurso($cursoId, $maestroId) {
        try {
            $sql = "DELETE FROM cursos WHERE id = :id AND maestro_id = :maestro_id";
            $stmt = $this->pdo->prepare($sql);
            $stmt->bindParam(':id', $cursoId, PDO::PARAM_INT);
            $stmt->bindParam(':maestro_id', $maestroId, PDO::PARAM_INT);
            $stmt->execute();
            return $stmt->rowCount() > 0;
        } catch (PDOException $e) {
            error_log("Error eliminando curso: " . $e->getMessage());
            return false;
        }
    }

    public function obtenerCursoPorId($cursoId) {
        try {
            $sql = "SELECT * FROM cursos WHERE id = :id";
            $stmt = $this->pdo->prepare($sql);
            $stmt->bindParam(':id', $cursoId, PDO::PARAM_INT);
            $stmt->execute();
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Error obteniendo curso por ID: " . $e->getMessage());
            return false;
        }
    }

    public function obtenerHorarioPorCursoId($cursoId) {
        try {
            $sql = "SELECT dia_semana, hora_inicio, hora_fin FROM horarios WHERE curso_id = :curso_id";
            $stmt = $this->pdo->prepare($sql);
            $stmt->bindParam(':curso_id', $cursoId, PDO::PARAM_INT);
            $stmt->execute();
            $horario = [];
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $horario[$row['dia_semana']] = [
                    'hora_inicio' => substr($row['hora_inicio'], 0, 5),
                    'hora_fin' => substr($row['hora_fin'], 0, 5)
                ];
            }
            return $horario;
        } catch (PDOException $e) {
            error_log("Error obteniendo horario: " . $e->getMessage());
            return [];
        }
    }
    
    public function actualizarHorario($cursoId, $horarios) {
        $sqlDelete = "DELETE FROM horarios WHERE curso_id = :curso_id";
        $stmtDelete = $this->pdo->prepare($sqlDelete);
        $stmtDelete->bindParam(':curso_id', $cursoId, PDO::PARAM_INT);
        $stmtDelete->execute();

        if (empty($horarios)) return true;

        $sqlInsert = "INSERT INTO horarios (curso_id, dia_semana, hora_inicio, hora_fin) VALUES (:curso_id, :dia, :inicio, :fin)";
        $stmtInsert = $this->pdo->prepare($sqlInsert);

        foreach ($horarios as $dia => $horas) {
            if (!empty($horas['start']) && !empty($horas['end'])) {
                $stmtInsert->execute([
                    ':curso_id' => $cursoId,
                    ':dia' => $dia,
                    ':inicio' => $horas['start'],
                    ':fin' => $horas['end']
                ]);
            }
        }
        return true;
    }
}
?>