<?php
// Habilitar reporte de errores para debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'AuthController.php';
require_once '../Modelo/Curso.php';
require_once '../Modelo/Maestro.php';
require_once '../Modelo/Conexion.php';

class CursoController {
    private $pdo;
    private $cursoModel;
    private $maestroModel;

    public function __construct() {
        $this->pdo = Conexion::getConexion();
        $this->cursoModel = new Curso();
        $this->maestroModel = new Maestro();
    }

    /**
     * Orquesta las solicitudes AJAX para cursos.
     */
    public function procesarSolicitud() {
        if (!AuthController::estaAutenticado() || !AuthController::tieneRol('maestro')) {
            $this->enviarRespuestaJSON(false, 'Acceso no autorizado.');
            return;
        }

        if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['action']) && $_GET['action'] === 'get_course') {
            $this->obtenerCursoParaEditar();
            return;
        }

        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['tipo_actualizacion'])) {
            $tipo = $_POST['tipo_actualizacion'];
            switch ($tipo) {
                case 'crear_curso':
                    $this->crearCurso();
                    break;
                case 'editar_curso':
                    $this->actualizarCurso();
                    break;
                case 'eliminar_curso':
                    $this->eliminarCurso();
                    break;
                default:
                    $this->enviarRespuestaJSON(false, 'Tipo de operación no válida.');
            }
            return;
        }
        
        $this->enviarRespuestaJSON(false, 'Solicitud no reconocida.');
    }

    private function crearCurso() {
        try {
            $maestro = $this->maestroModel->obtenerPorUsuarioId($_SESSION['usuario_id']);
            if (!$maestro) throw new Exception('No se pudo verificar la identidad del maestro.');
            if (empty($_POST['nombre']) || empty($_POST['grado']) || empty($_POST['especialidad'])) throw new Exception('Nombre, grado y especialidad son obligatorios.');

            $rutaImagen = $this->manejarSubidaImagen('imagen', $maestro['id']);

            $datosCurso = [
                'nombre' => trim($_POST['nombre']),
                'especialidad' => trim($_POST['especialidad']),
                'maestro_id' => $maestro['id'],
                'grado' => trim($_POST['grado']),
                'anio_escolar' => (int)($_POST['anio_escolar'] ?? date('Y')),
                'descripcion' => trim($_POST['descripcion'] ?? ''),
                'icono' => trim($_POST['icono'] ?? 'book'),
                'imagen' => $rutaImagen
            ];
            
            $this->pdo->beginTransaction();

            $cursoId = $this->cursoModel->crearCurso($datosCurso);
            if (!$cursoId) throw new Exception('Hubo un error al guardar los datos del curso.');

            $horario = json_decode($_POST['horario'] ?? '[]', true);
            if (is_array($horario)) $this->cursoModel->actualizarHorario($cursoId, $horario);
            
            $this->pdo->commit();
            $this->enviarRespuestaJSON(true, 'Curso creado exitosamente.', ['curso_id' => $cursoId]);

        } catch (Exception $e) {
            if ($this->pdo->inTransaction()) $this->pdo->rollBack();
            $this->enviarRespuestaJSON(false, $e->getMessage());
        }
    }

    private function actualizarCurso() {
        try {
            $cursoId = (int)($_POST['id'] ?? 0);
            if ($cursoId === 0) throw new Exception('ID de curso no válido.');

            $maestro = $this->maestroModel->obtenerPorUsuarioId($_SESSION['usuario_id']);
            if (!$maestro) throw new Exception('No se pudo verificar la identidad del maestro.');
            
            $cursoActual = $this->cursoModel->obtenerCursoPorId($cursoId);
            $rutaImagen = $cursoActual['imagen'] ?? null;

            if (isset($_FILES['imagen']) && $_FILES['imagen']['error'] === UPLOAD_ERR_OK) {
                if ($rutaImagen && file_exists('../Vista/' . $rutaImagen)) unlink('../Vista/' . $rutaImagen);
                $rutaImagen = $this->manejarSubidaImagen('imagen', $maestro['id']);
            }

            $datosCurso = [
                'nombre' => trim($_POST['nombre']),
                'especialidad' => trim($_POST['especialidad']),
                'grado' => trim($_POST['grado']),
                'anio_escolar' => (int)($_POST['anio_escolar'] ?? date('Y')),
                'descripcion' => trim($_POST['descripcion'] ?? ''),
                'icono' => trim($_POST['icono'] ?? 'book'),
                'imagen' => $rutaImagen
            ];
            
            $this->pdo->beginTransaction();
            $this->cursoModel->actualizarCurso($cursoId, $maestro['id'], $datosCurso);
            
            $horario = json_decode($_POST['horario'] ?? '[]', true);
            if (is_array($horario)) $this->cursoModel->actualizarHorario($cursoId, $horario);

            $this->pdo->commit();
            $this->enviarRespuestaJSON(true, 'Curso actualizado exitosamente.');

        } catch (Exception $e) {
            if ($this->pdo->inTransaction()) $this->pdo->rollBack();
            $this->enviarRespuestaJSON(false, $e->getMessage());
        }
    }

    private function eliminarCurso() {
        try {
            $cursoId = (int)($_POST['id'] ?? 0);
            if ($cursoId === 0) throw new Exception('ID de curso no válido.');

            $maestro = $this->maestroModel->obtenerPorUsuarioId($_SESSION['usuario_id']);
            if (!$maestro) throw new Exception('No se pudo verificar la identidad del maestro.');

            $cursoActual = $this->cursoModel->obtenerCursoPorId($cursoId);
            if ($cursoActual && !empty($cursoActual['imagen']) && file_exists('../Vista/' . $cursoActual['imagen'])) {
                unlink('../Vista/' . $cursoActual['imagen']);
            }

            if ($this->cursoModel->eliminarCurso($cursoId, $maestro['id'])) {
                $this->enviarRespuestaJSON(true, 'Curso eliminado exitosamente.');
            } else {
                throw new Exception('No se pudo eliminar el curso o no tienes permiso.');
            }
        } catch (Exception $e) {
            $this->enviarRespuestaJSON(false, $e->getMessage());
        }
    }

    private function obtenerCursoParaEditar() {
        try {
            $cursoId = (int)($_GET['id'] ?? 0);
            if ($cursoId === 0) throw new Exception('ID de curso no válido.');
            
            $curso = $this->cursoModel->obtenerCursoPorId($cursoId);
            if (!$curso) throw new Exception('Curso no encontrado.');

            $curso['horarios'] = $this->cursoModel->obtenerHorarioPorCursoId($cursoId);
            $this->enviarRespuestaJSON(true, 'Datos obtenidos', $curso);
        } catch (Exception $e) {
            $this->enviarRespuestaJSON(false, $e->getMessage());
        }
    }
    
    private function manejarSubidaImagen($fileKey, $maestroId) {
        if (isset($_FILES[$fileKey]) && $_FILES[$fileKey]['error'] === UPLOAD_ERR_OK) {
            $file = $_FILES[$fileKey];
            $allowed = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
            $fileExt = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));

            if (in_array($fileExt, $allowed)) {
                $fileNameNew = 'curso_' . $maestroId . '_' . uniqid('', true) . '.' . $fileExt;
                $uploadPath = '../Vista/img/cursos/';
                if (!is_dir($uploadPath)) mkdir($uploadPath, 0775, true);
                $fileDestination = $uploadPath . $fileNameNew;
                if (move_uploaded_file($file['tmp_name'], $fileDestination)) {
                    return './img/cursos/' . $fileNameNew;
                }
            }
        }
        return null;
    }

    private function enviarRespuestaJSON($success, $message, $data = null) {
        header('Content-Type: application/json');
        echo json_encode(['success' => $success, 'message' => $message, 'data' => $data]);
        exit;
    }
}

$controller = new CursoController();
$controller->procesarSolicitud();