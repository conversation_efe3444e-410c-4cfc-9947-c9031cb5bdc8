<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Cursos</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="./Css/cursos_m.css">
</head>
<body>
    <div class="container">
        <main class="main-content">
            <div class="content-wrapper">
                <section class="courses-grid">
                    <div class="course-card" data-course-name="matematicas">
                        <div class="course-card-header">
                            <img src="./img/curso_default.png" alt="Matemáticas">
                            <div class="course-icon"><span class="material-icons">calculate</span></div>
                            <div class="course-actions">
                                <button class="course-action-btn edit-btn" data-id="1"><span class="material-icons">edit</span></button>
                                <button class="course-action-btn delete-btn" data-id="1"><span class="material-icons">delete</span></button>
                            </div>
                        </div>
                        <div class="course-card-body">
                            <h3>Matemáticas</h3>
                            <p class="course-grade">1° Primaria</p>
                            <div class="course-details">
                                <div class="course-detail">
                                    <span class="material-icons">schedule</span>
                                    <span>Lunes de 04:22 a 23:22</span>
                                </div>
                                <div class="course-detail">
                                    <span class="material-icons">people</span>
                                    <span>0 estudiantes</span>
                                </div>
                            </div>
                        </div>
                        <div class="course-card-footer">
                            <a href="#" class="view-course-btn">
                                <span class="material-icons">visibility</span>
                                Ver curso
                            </a>
                        </div>
                    </div>
                </section>
            </div>
        </main>
    </div>

    <!-- Modal para crear/editar curso -->
    <div id="course-modal" class="modal-overlay">
        <div class="modal-content">
            <form id="course-form" method="POST" enctype="multipart/form-data" novalidate>
                <div class="modal-header">
                    <h3 id="modal-title">Editar Curso</h3>
                    <button type="button" class="modal-close-btn"><span class="material-icons">close</span></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="course-id" name="id">
                    <input type="hidden" name="especialidad" value="Matemáticas">
                    <div class="form-group">
                        <label for="course-name">Nombre del Curso</label>
                        <input type="text" id="course-name" name="nombre" required>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="course-grade">Grado</label>
                            <select id="course-grade" name="grado" required>
                                <option value="" disabled selected>Seleccionar grado</option>
                                <option value="1">1° Primaria</option>
                                <option value="2">2° Primaria</option>
                                <option value="3">3° Primaria</option>
                                <option value="4">4° Primaria</option>
                                <option value="5">5° Primaria</option>
                                <option value="6">6° Primaria</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="course-year">Año Escolar</label>
                            <input type="number" id="course-year" name="anio_escolar" value="2024" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="course-description">Descripción (opcional)</label>
                        <textarea id="course-description" name="descripcion" rows="3" placeholder="Descripción del curso..."></textarea>
                    </div>
                    <div class="form-group">
                        <label for="course-icon">Ícono del Curso</label>
                        <select id="course-icon" name="icono" required>
                            <option value="calculate" selected>Matemáticas</option>
                            <option value="science">Ciencia</option>
                            <option value="menu_book">Comunicación</option>
                            <option value="public">Personal Social</option>
                            <option value="brush">Arte</option>
                            <option value="directions_run">Ed. Física</option>
                        </select>
                    </div>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn-secondary modal-close-btn">Cancelar</button>
                    <button type="submit" class="btn-primary">Guardar Curso</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Simulación de datos del curso para prueba
        const mockCourseData = {
            success: true,
            data: {
                id: 1,
                nombre: "Matemáticas",
                grado: "1",
                anio_escolar: "2024",
                descripcion: "Curso de matemáticas básicas",
                icono: "calculate",
                horarios: {
                    lunes: { hora_inicio: "08:00", hora_fin: "09:00" },
                    miercoles: { hora_inicio: "10:00", hora_fin: "11:00" }
                }
            }
        };

        document.addEventListener("DOMContentLoaded", () => {
            const courseModal = document.getElementById("course-modal");
            const courseForm = document.getElementById("course-form");
            const modalTitle = document.getElementById("modal-title");

            const openModal = (modal) => modal && modal.classList.add("active");
            const closeModal = () => document.querySelectorAll(".modal-overlay.active").forEach(m => m.classList.remove("active"));
            
            document.querySelectorAll(".modal-close-btn").forEach(btn => btn.addEventListener("click", closeModal));
            document.querySelectorAll('.modal-overlay').forEach(m => m.addEventListener('click', e => e.target === m && closeModal()));

            const coursesGrid = document.querySelector('.courses-grid');
            if (!coursesGrid) {
                console.error('No se encontró el elemento .courses-grid');
                return;
            }
            
            coursesGrid.addEventListener('click', (e) => {
                const editBtn = e.target.closest('.edit-btn');
                
                if (editBtn) {
                    console.log('Botón editar clickeado, ID:', editBtn.dataset.id);

                    // Hacer petición real al controlador de prueba
                    fetch(`./test_controller.php?action=get_course&id=${editBtn.dataset.id}`)
                        .then(res => res.json())
                        .then(res => {
                            console.log('Respuesta del servidor:', res);

                            if (res.success) {
                        const curso = res.data;
                        courseForm.reset();
                        modalTitle.textContent = "Editar Curso";
                        
                        // Mapear los campos del curso a los inputs del formulario
                        const fieldMapping = {
                            'id': 'course-id',
                            'nombre': 'course-name',
                            'grado': 'course-grade',
                            'anio_escolar': 'course-year',
                            'descripcion': 'course-description',
                            'icono': 'course-icon'
                        };
                        
                        Object.keys(fieldMapping).forEach(key => {
                            const input = courseForm.querySelector(`#${fieldMapping[key]}`);
                            if (input && curso[key] !== undefined) {
                                input.value = curso[key] || '';
                                console.log(`Campo ${key} -> ${fieldMapping[key]}: ${curso[key]}`);
                            }
                        });
                        
                                console.log('Abriendo modal de edición');
                                openModal(courseModal);
                            } else {
                                alert('Error: ' + res.message);
                            }
                        })
                        .catch(err => {
                            console.error('Error en la petición:', err);
                            alert('Error de conexión con el servidor.');
                        });
                }
            });
        });
    </script>
</body>
</html>
