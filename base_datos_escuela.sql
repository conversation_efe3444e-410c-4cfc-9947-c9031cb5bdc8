-- Base de datos para Sistema Educativo Escuela NV
-- <PERSON>rado basado en los requerimientos del archivo "Cosas que hay en la web.txt"

CREATE DATABASE IF NOT EXISTS escuela_nv;
USE escuela_nv;

-- =============================================
-- TABLA DE CONFIGURACIÓN DE BIMESTRES
-- =============================================
CREATE TABLE bimestres (
    id INT PRIMARY KEY AUTO_INCREMENT,
    numero INT NOT NULL, -- 1, 2, 3, 4
    nombre VARCHAR(50) NOT NULL, -- "Primer Bimestre", "Segundo Bimestre", etc.
    fecha_inicio DATE NOT NULL,
    fecha_fin DATE NOT NULL,
    anio_escolar YEAR NOT NULL,
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- =============================================
-- TABLA DE USUARIOS (TABLA PRINCIPAL)
-- =============================================
CREATE TABLE usuarios (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nombre_usuario VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    rol ENUM('estudiante', 'maestro', 'padre', 'administrador') NOT NULL,
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- =============================================
-- TABLA DE PERSONAS (INFORMACIÓN PERSONAL)
-- =============================================
CREATE TABLE personas (
    id INT PRIMARY KEY AUTO_INCREMENT,
    usuario_id INT,
    nombres VARCHAR(100) NOT NULL,
    apellido_paterno VARCHAR(50) NOT NULL,
    apellido_materno VARCHAR(50) NOT NULL,
    dni VARCHAR(8) UNIQUE,
    fecha_nacimiento DATE,
    edad INT,
    sexo ENUM('masculino', 'femenino') NOT NULL,
    direccion TEXT,
    telefono VARCHAR(15),
    foto_perfil MEDIUMBLOB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (usuario_id) REFERENCES usuarios(id) ON DELETE CASCADE
);

-- =============================================
-- TABLA DE ESTUDIANTES
-- =============================================
CREATE TABLE estudiantes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    persona_id INT NOT NULL,
    grado_actual VARCHAR(20) NOT NULL, -- "inicial-3", "inicial-4", "inicial-5", "primaria-1", etc.
    anio_escolar YEAR NOT NULL,
    promedio_general DECIMAL(4,2) DEFAULT 0.00,
    maestro_tutor_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (persona_id) REFERENCES personas(id) ON DELETE CASCADE,
    FOREIGN KEY (maestro_tutor_id) REFERENCES maestros(id)
);

-- =============================================
-- TABLA DE MAESTROS
-- =============================================
CREATE TABLE maestros (
    id INT PRIMARY KEY AUTO_INCREMENT,
    persona_id INT NOT NULL,
    especialidad ENUM('Matemáticas', 'Ciencia y Tecnología', 'Comunicación', 'Inglés', 'Arte', 'Personal Social', 'Educación Física') NOT NULL,
    nivel_educativo ENUM('inicial', 'primaria') NOT NULL,
    grado_tutor VARCHAR(20), -- Grado del cual es tutor
    fecha_contratacion DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (persona_id) REFERENCES personas(id) ON DELETE CASCADE
);

-- =============================================
-- TABLA DE PADRES/APODERADOS
-- =============================================
CREATE TABLE padres (
    id INT PRIMARY KEY AUTO_INCREMENT,
    persona_id INT NOT NULL,
    tipo_apoderado ENUM('padre', 'madre', 'tutor legal') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (persona_id) REFERENCES personas(id) ON DELETE CASCADE
);

-- =============================================
-- TABLA DE ADMINISTRADORES
-- =============================================
CREATE TABLE administradores (
    id INT PRIMARY KEY AUTO_INCREMENT,
    persona_id INT NOT NULL,
    cargo VARCHAR(100) NOT NULL,
    departamento VARCHAR(100),
    fecha_contratacion DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (persona_id) REFERENCES personas(id) ON DELETE CASCADE
);

-- =============================================
-- TABLA DE RELACIÓN PADRE-ESTUDIANTE
-- =============================================
CREATE TABLE padre_estudiante (
    id INT PRIMARY KEY AUTO_INCREMENT,
    padre_id INT NOT NULL,
    estudiante_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (padre_id) REFERENCES padres(id) ON DELETE CASCADE,
    FOREIGN KEY (estudiante_id) REFERENCES estudiantes(id) ON DELETE CASCADE,
    UNIQUE KEY unique_padre_estudiante (padre_id, estudiante_id)
);

-- =============================================
-- TABLA DE CONTACTOS DE EMERGENCIA
-- =============================================
CREATE TABLE contactos_emergencia (
    id INT PRIMARY KEY AUTO_INCREMENT,
    persona_id INT NOT NULL,
    nombre_contacto VARCHAR(100) NOT NULL,
    telefono_principal VARCHAR(15) NOT NULL,
    telefono_alternativo VARCHAR(15),
    email VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (persona_id) REFERENCES personas(id) ON DELETE CASCADE
);

-- =============================================
-- TABLA DE CURSOS
-- =============================================
CREATE TABLE cursos (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nombre VARCHAR(100) NOT NULL,
    especialidad ENUM('Matemáticas', 'Ciencia y Tecnología', 'Comunicación', 'Inglés', 'Arte', 'Personal Social', 'Educación Física') NOT NULL,
    maestro_id INT NOT NULL,
    grado VARCHAR(20) NOT NULL,
    anio_escolar YEAR NOT NULL,
    descripcion TEXT,
    icono VARCHAR(50),
    imagen VARCHAR(255),
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (maestro_id) REFERENCES maestros(id)
    -- NOTA: La validación de especialidad maestro-curso se manejará en PHP
    -- Los CHECK CONSTRAINTS con subconsultas no están permitidos en MySQL
);

-- =============================================
-- TABLA DE HORARIOS
-- =============================================
CREATE TABLE horarios (
    id INT PRIMARY KEY AUTO_INCREMENT,
    curso_id INT NOT NULL,
    dia_semana ENUM('lunes', 'martes', 'miercoles', 'jueves', 'viernes') NOT NULL,
    hora_inicio TIME NOT NULL,
    hora_fin TIME NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (curso_id) REFERENCES cursos(id) ON DELETE CASCADE
);

-- =============================================
-- TABLA DE INSCRIPCIONES (ESTUDIANTE-CURSO)
-- =============================================
CREATE TABLE inscripciones (
    id INT PRIMARY KEY AUTO_INCREMENT,
    estudiante_id INT NOT NULL,
    curso_id INT NOT NULL,
    fecha_inscripcion DATE DEFAULT (CURRENT_DATE),
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (estudiante_id) REFERENCES estudiantes(id) ON DELETE CASCADE,
    FOREIGN KEY (curso_id) REFERENCES cursos(id) ON DELETE CASCADE,
    UNIQUE KEY unique_estudiante_curso (estudiante_id, curso_id)
);

-- =============================================
-- TABLA DE ENLACES DE VIDEOCONFERENCIA
-- =============================================
CREATE TABLE enlaces_videoconferencia (
    id INT PRIMARY KEY AUTO_INCREMENT,
    curso_id INT NOT NULL,
    titulo VARCHAR(100) NOT NULL,
    url TEXT NOT NULL,
    fecha DATE NOT NULL,
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (curso_id) REFERENCES cursos(id) ON DELETE CASCADE
);

-- =============================================
-- TABLA DE SEMANAS/PERÍODOS ACADÉMICOS (MOVIDA AQUÍ PARA RESOLVER DEPENDENCIAS)
-- =============================================
CREATE TABLE semanas_academicas (
    id INT PRIMARY KEY AUTO_INCREMENT,
    curso_id INT NOT NULL,
    numero_semana INT NOT NULL,
    titulo VARCHAR(200) NOT NULL,
    descripcion TEXT,
    fecha_inicio DATE NOT NULL,
    fecha_fin DATE NOT NULL,
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (curso_id) REFERENCES cursos(id) ON DELETE CASCADE,
    UNIQUE KEY unique_semana_curso (curso_id, numero_semana)
);

-- =============================================
-- TABLA DE SESIONES (CARPETAS DE CONTENIDO)
-- =============================================
CREATE TABLE sesiones (
    id INT PRIMARY KEY AUTO_INCREMENT,
    curso_id INT NOT NULL,
    semana_id INT, -- Relación con semanas académicas
    titulo VARCHAR(100) NOT NULL,
    descripcion TEXT,
    fecha_inicio DATE NOT NULL,
    fecha_fin DATE NOT NULL,
    orden INT DEFAULT 1,
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (curso_id) REFERENCES cursos(id) ON DELETE CASCADE,
    FOREIGN KEY (semana_id) REFERENCES semanas_academicas(id) ON DELETE SET NULL
);

-- =============================================
-- TABLA DE CONTENIDO (TAREAS, EXÁMENES, DOCUMENTOS, ETC.)
-- =============================================
CREATE TABLE contenido (
    id INT PRIMARY KEY AUTO_INCREMENT,
    sesion_id INT NOT NULL,
    tipo ENUM('anuncio', 'tarea', 'examen', 'documento', 'presentacion', 'enlace', 'video', 'participacion') NOT NULL,
    titulo VARCHAR(200) NOT NULL,
    descripcion TEXT,
    imagen VARCHAR(255),
    archivo VARCHAR(255),
    url TEXT,
    fecha_limite DATE,
    hora_limite TIME,
    puntos INT DEFAULT 0,
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (sesion_id) REFERENCES sesiones(id) ON DELETE CASCADE
);

-- =============================================
-- TABLA DE ENTREGAS DE TAREAS/EXÁMENES
-- =============================================
CREATE TABLE entregas (
    id INT PRIMARY KEY AUTO_INCREMENT,
    contenido_id INT NOT NULL,
    estudiante_id INT NOT NULL,
    archivo VARCHAR(255),
    comentario_estudiante TEXT,
    fecha_entrega TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    estado ENUM('pendiente', 'entregado', 'tardio', 'calificado') DEFAULT 'pendiente',
    calificacion DECIMAL(4,2),
    comentario_maestro TEXT,
    fecha_calificacion TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (contenido_id) REFERENCES contenido(id) ON DELETE CASCADE,
    FOREIGN KEY (estudiante_id) REFERENCES estudiantes(id) ON DELETE CASCADE,
    UNIQUE KEY unique_contenido_estudiante (contenido_id, estudiante_id)
);

-- =============================================
-- TABLA DE PARTICIPACIÓN
-- =============================================
CREATE TABLE participacion (
    id INT PRIMARY KEY AUTO_INCREMENT,
    contenido_id INT NOT NULL,
    estudiante_id INT NOT NULL,
    nota_participacion DECIMAL(4,2) NOT NULL,
    comentario TEXT,
    fecha_registro DATE DEFAULT (CURRENT_DATE),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (contenido_id) REFERENCES contenido(id) ON DELETE CASCADE,
    FOREIGN KEY (estudiante_id) REFERENCES estudiantes(id) ON DELETE CASCADE,
    UNIQUE KEY unique_participacion (contenido_id, estudiante_id)
);

-- =============================================
-- TABLA DE CALIFICACIONES POR BIMESTRE
-- =============================================
CREATE TABLE calificaciones (
    id INT PRIMARY KEY AUTO_INCREMENT,
    estudiante_id INT NOT NULL,
    curso_id INT NOT NULL,
    bimestre_id INT NOT NULL,
    promedio_tareas DECIMAL(4,2) DEFAULT 0.00,
    promedio_examenes DECIMAL(4,2) DEFAULT 0.00,
    promedio_participacion DECIMAL(4,2) DEFAULT 0.00,
    calificacion_final DECIMAL(4,2) DEFAULT 0.00,
    comentario_maestro TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (estudiante_id) REFERENCES estudiantes(id) ON DELETE CASCADE,
    FOREIGN KEY (curso_id) REFERENCES cursos(id) ON DELETE CASCADE,
    FOREIGN KEY (bimestre_id) REFERENCES bimestres(id),
    UNIQUE KEY unique_calificacion_bimestre (estudiante_id, curso_id, bimestre_id)
);

-- =============================================
-- TABLA DE ASISTENCIAS DE ESTUDIANTES
-- =============================================
CREATE TABLE asistencias_estudiantes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    estudiante_id INT NOT NULL,
    curso_id INT NOT NULL,
    fecha DATE NOT NULL,
    estado ENUM('presente', 'falta', 'tardanza', 'justificado') NOT NULL,
    observaciones TEXT,
    registrado_por INT NOT NULL, -- ID del maestro que registró
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (estudiante_id) REFERENCES estudiantes(id) ON DELETE CASCADE,
    FOREIGN KEY (curso_id) REFERENCES cursos(id) ON DELETE CASCADE,
    FOREIGN KEY (registrado_por) REFERENCES maestros(id),
    UNIQUE KEY unique_asistencia_dia (estudiante_id, curso_id, fecha)
);

-- =============================================
-- TABLA DE ASISTENCIAS DE MAESTROS
-- =============================================
CREATE TABLE asistencias_maestros (
    id INT PRIMARY KEY AUTO_INCREMENT,
    maestro_id INT NOT NULL,
    fecha DATE NOT NULL,
    hora_entrada TIME,
    hora_salida TIME,
    estado ENUM('presente', 'falta', 'tardanza', 'salida_anticipada') NOT NULL,
    observaciones TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (maestro_id) REFERENCES maestros(id) ON DELETE CASCADE,
    UNIQUE KEY unique_asistencia_maestro_dia (maestro_id, fecha)
);

-- =============================================
-- TABLA DE JUSTIFICACIONES DE ASISTENCIA MAESTROS
-- =============================================
CREATE TABLE justificaciones_maestros (
    id INT PRIMARY KEY AUTO_INCREMENT,
    asistencia_id INT NOT NULL,
    descripcion TEXT NOT NULL,
    estado ENUM('pendiente', 'aprobada', 'rechazada') DEFAULT 'pendiente',
    fecha_solicitud TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    fecha_respuesta TIMESTAMP NULL,
    respondido_por INT, -- ID del administrador que respondió
    comentario_respuesta TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (asistencia_id) REFERENCES asistencias_maestros(id) ON DELETE CASCADE,
    FOREIGN KEY (respondido_por) REFERENCES administradores(id)
);

-- =============================================
-- TABLA DE ANUNCIOS
-- =============================================
CREATE TABLE anuncios (
    id INT PRIMARY KEY AUTO_INCREMENT,
    titulo VARCHAR(200) NOT NULL,
    descripcion_corta TEXT,
    contenido TEXT NOT NULL,
    imagen VARCHAR(255),
    fecha_publicacion DATE DEFAULT (CURRENT_DATE),
    estado ENUM('borrador', 'publicado') DEFAULT 'borrador',
    administrador_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (administrador_id) REFERENCES administradores(id)
);

-- =============================================
-- TABLA DE MENSAJES
-- =============================================
CREATE TABLE mensajes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    remitente_id INT NOT NULL,
    destinatario_id INT NOT NULL,
    tipo_asunto ENUM('consulta_examen', 'consulta_tarea', 'consulta_clase', 'otro') NOT NULL,
    asunto VARCHAR(200) NOT NULL,
    mensaje TEXT NOT NULL,
    archivo_adjunto VARCHAR(255),
    fecha_envio TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    leido BOOLEAN DEFAULT FALSE,
    fecha_lectura TIMESTAMP NULL,
    curso_id INT, -- Para mensajes relacionados con un curso específico
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (remitente_id) REFERENCES usuarios(id),
    FOREIGN KEY (destinatario_id) REFERENCES usuarios(id),
    FOREIGN KEY (curso_id) REFERENCES cursos(id)
);

-- =============================================
-- TABLA DE PREINSCRIPCIONES
-- =============================================
CREATE TABLE preinscripciones (
    id INT PRIMARY KEY AUTO_INCREMENT,
    numero_solicitud VARCHAR(20) UNIQUE NOT NULL,
    nombre_estudiante VARCHAR(100) NOT NULL,
    apellido_paterno VARCHAR(50) NOT NULL,
    apellido_materno VARCHAR(50) NOT NULL,
    dni_estudiante VARCHAR(8) NOT NULL,
    dni_padre VARCHAR(8) NOT NULL,
    correo_apoderado VARCHAR(100) NOT NULL,
    telefono_apoderado VARCHAR(15) NOT NULL,
    grado_interes VARCHAR(20) NOT NULL, -- "inicial-3", "inicial-4", "inicial-5", "primaria-1", etc.
    estado ENUM('pendiente', 'revisado') DEFAULT 'pendiente',
    fecha_solicitud TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    fecha_revision TIMESTAMP NULL,
    revisado_por INT, -- ID del administrador que revisó
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (revisado_por) REFERENCES administradores(id)
);

-- =============================================
-- TABLA DE PAGOS
-- =============================================
CREATE TABLE pagos (
    id INT PRIMARY KEY AUTO_INCREMENT,
    padre_id INT NOT NULL,
    estudiante_id INT NOT NULL,
    tipo ENUM('matricula', 'pension') NOT NULL,
    mes INT, -- Para pensiones (1-12), NULL para matrícula
    anio YEAR NOT NULL,
    monto DECIMAL(8,2) NOT NULL,
    fecha_vencimiento DATE NOT NULL,
    fecha_pago DATE,
    estado ENUM('pendiente', 'pagado', 'vencido') DEFAULT 'pendiente',
    metodo_pago ENUM('efectivo', 'transferencia', 'tarjeta', 'deposito') NULL,
    comprobante VARCHAR(255),
    observaciones TEXT,
    registrado_por INT, -- ID del administrador que registró el pago
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (padre_id) REFERENCES padres(id) ON DELETE CASCADE,
    FOREIGN KEY (estudiante_id) REFERENCES estudiantes(id) ON DELETE CASCADE,
    FOREIGN KEY (registrado_por) REFERENCES administradores(id)
);

-- =============================================
-- TABLA DE ACTIVIDADES DEL SISTEMA (LOG)
-- =============================================
CREATE TABLE actividades_sistema (
    id INT PRIMARY KEY AUTO_INCREMENT,
    usuario_id INT NOT NULL,
    accion VARCHAR(100) NOT NULL,
    descripcion TEXT,
    tabla_afectada VARCHAR(50),
    registro_id INT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (usuario_id) REFERENCES usuarios(id)
);

-- =============================================
-- TABLA DE RANKING DE ESTUDIANTES
-- =============================================
-- TABLA ELIMINADA: ranking_estudiantes
-- RAZÓN: PHP puede calcular el ranking dinámicamente usando ORDER BY y ROW_NUMBER()

-- =============================================
-- TABLA ELIMINADA: estadisticas_sistema
-- RAZÓN: PHP puede calcular estas estadísticas dinámicamente con COUNT() en cada tabla
-- =============================================

-- =============================================
-- TABLA DE HORAS TRABAJADAS MAESTROS
-- =============================================
CREATE TABLE horas_trabajadas_maestros (
    id INT PRIMARY KEY AUTO_INCREMENT,
    maestro_id INT NOT NULL,
    fecha DATE NOT NULL,
    horas_trabajadas DECIMAL(4,2) NOT NULL,
    descripcion TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (maestro_id) REFERENCES maestros(id) ON DELETE CASCADE,
    UNIQUE KEY unique_maestro_fecha (maestro_id, fecha)
);

-- =============================================
-- TABLA ELIMINADA: estados_cursos (INNECESARIA)
-- RAZÓN: El estado del curso se calcula dinámicamente en PHP
-- =============================================

-- =============================================
-- TABLA DE COMENTARIOS PARA PADRES
-- =============================================
CREATE TABLE comentarios_padres (
    id INT PRIMARY KEY AUTO_INCREMENT,
    estudiante_id INT NOT NULL,
    curso_id INT NOT NULL,
    bimestre_id INT NOT NULL,
    maestro_id INT NOT NULL,
    comentario TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (estudiante_id) REFERENCES estudiantes(id) ON DELETE CASCADE,
    FOREIGN KEY (curso_id) REFERENCES cursos(id) ON DELETE CASCADE,
    FOREIGN KEY (bimestre_id) REFERENCES bimestres(id),
    FOREIGN KEY (maestro_id) REFERENCES maestros(id),
    UNIQUE KEY unique_comentario_bimestre (estudiante_id, curso_id, bimestre_id)
);

-- =============================================
-- TABLA DE ARCHIVOS DE JUSTIFICACIÓN
-- =============================================
CREATE TABLE archivos_justificacion (
    id INT PRIMARY KEY AUTO_INCREMENT,
    justificacion_id INT NOT NULL,
    nombre_archivo VARCHAR(255) NOT NULL,
    ruta_archivo VARCHAR(500) NOT NULL,
    tamaño_kb INT,
    tipo_archivo VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (justificacion_id) REFERENCES justificaciones_maestros(id) ON DELETE CASCADE
);

-- =============================================
-- TABLA ELIMINADA: resumen_tareas_estudiante
-- RAZÓN: PHP puede calcular estos datos dinámicamente consultando la tabla 'entregas'
-- =============================================

-- =============================================
-- TABLA ELIMINADA: filtros_anuncios (INNECESARIA)
-- RAZÓN: Los filtros se manejan mejor directo en PHP
-- =============================================

-- =============================================
-- TABLA ELIMINADA: iconos_cursos (INNECESARIA)
-- RAZÓN: Los iconos van mejor como campo directo en tabla 'cursos'
-- =============================================

-- =============================================
-- TABLA DE CRONOGRAMA DE PAGOS
-- =============================================
CREATE TABLE cronograma_pagos (
    id INT PRIMARY KEY AUTO_INCREMENT,
    estudiante_id INT NOT NULL,
    anio_escolar YEAR NOT NULL,
    mes INT NOT NULL, -- 1-12
    concepto ENUM('matricula', 'pension') NOT NULL,
    monto DECIMAL(8,2) NOT NULL,
    fecha_vencimiento DATE NOT NULL,
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (estudiante_id) REFERENCES estudiantes(id) ON DELETE CASCADE,
    UNIQUE KEY unique_cronograma (estudiante_id, anio_escolar, mes, concepto)
);

-- =============================================
-- TABLA ELIMINADA: informacion_institucional (INNECESARIA)
-- RAZÓN: Esta información va mejor en archivos estáticos o config
-- =============================================

-- =============================================
-- TABLA DE CALIFICACIONES DE EXÁMENES
-- =============================================
CREATE TABLE calificaciones_examenes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    estudiante_id INT NOT NULL,
    contenido_id INT NOT NULL, -- Referencia al examen en la tabla contenido
    calificacion DECIMAL(4,2) NOT NULL,
    comentario_maestro TEXT,
    fecha_calificacion DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (estudiante_id) REFERENCES estudiantes(id) ON DELETE CASCADE,
    FOREIGN KEY (contenido_id) REFERENCES contenido(id) ON DELETE CASCADE,
    UNIQUE KEY unique_estudiante_examen (estudiante_id, contenido_id)
);

-- =============================================
-- TABLA ELIMINADA: estados_contenido (INNECESARIA)
-- RAZÓN: El contenido no necesita estados, se maneja con fechas límite
-- =============================================

-- =============================================
-- TABLA DE EDITOR DE TEXTO ENRIQUECIDO
-- =============================================
CREATE TABLE contenido_multimedia (
    id INT PRIMARY KEY AUTO_INCREMENT,
    contenido_id INT NOT NULL,
    tipo_multimedia ENUM('imagen', 'video', 'audio', 'documento') NOT NULL,
    nombre_archivo VARCHAR(255) NOT NULL,
    ruta_archivo VARCHAR(500) NOT NULL,
    tamaño_kb INT,
    orden INT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (contenido_id) REFERENCES contenido(id) ON DELETE CASCADE
);

-- =============================================
-- TABLA ELIMINADA: semanas_academicas (YA CREADA ANTERIORMENTE)
-- =============================================



-- =============================================
-- INSERTAR DATOS INICIALES
-- =============================================

-- Insertar bimestres para el año escolar 2025
INSERT INTO bimestres (numero, nombre, fecha_inicio, fecha_fin, anio_escolar) VALUES
(1, 'Primer Bimestre', '2025-03-01', '2025-04-30', 2025),
(2, 'Segundo Bimestre', '2025-05-01', '2025-06-30', 2025),
(3, 'Tercer Bimestre', '2025-08-01', '2025-09-30', 2025),
(4, 'Cuarto Bimestre', '2025-10-01', '2025-12-15', 2025);

-- Insertar usuario administrador por defecto
-- CREDENCIALES DE ACCESO:
-- Usuario: admin
-- Contraseña: password
-- Email: <EMAIL>
-- (El hash corresponde a la contraseña "password" con bcrypt)
INSERT INTO usuarios (nombre_usuario, email, password, rol) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'administrador');

INSERT INTO personas (usuario_id, nombres, apellido_paterno, apellido_materno, dni, fecha_nacimiento, sexo, direccion, telefono) VALUES
(1, 'Administrador', 'Sistema', 'Escuela', '12345678', '1980-01-01', 'masculino', 'Dirección de la escuela', '991663041');

INSERT INTO administradores (persona_id, cargo, departamento, fecha_contratacion) VALUES
(1, 'Administrador General', 'Administración', '2025-01-01');

-- Insertar filtros de anuncios (COMENTADO - TABLA ELIMINADA)
-- Actualizar iconos de cursos (usando Material Icons - compatible con HTML)
-- NOTA: Estos UPDATE se ejecutarán después de crear los cursos
UPDATE cursos SET icono = 'calculate' WHERE especialidad = 'Matemáticas';
UPDATE cursos SET icono = 'science' WHERE especialidad = 'Ciencia y Tecnología';
UPDATE cursos SET icono = 'menu_book' WHERE especialidad = 'Comunicación';
UPDATE cursos SET icono = 'language' WHERE especialidad = 'Inglés';
UPDATE cursos SET icono = 'brush' WHERE especialidad = 'Arte';
UPDATE cursos SET icono = 'history_edu' WHERE especialidad = 'Personal Social';
UPDATE cursos SET icono = 'sports_soccer' WHERE especialidad = 'Educación Física';



-- Insertar información institucional (COMENTADO - TABLA ELIMINADA)
-- Esta información va mejor en archivos estáticos o config PHP
/*
INSERT INTO informacion_institucional (tipo, titulo, contenido, orden) VALUES
('mision', 'Nuestra Misión', 'Brindar una formación que promueva el desarrollo integral de niños y niñas; formar personas autónomas, críticas, reflexivas, responsables, solidarias, comprometidas con su entorno, con conciencia ecológica, conservación del medio ambiente y de su salud, niños que sienten sus propias bases para la construcción de su proyecto de vida y contribuyan al desarrollo de la comunidad comprometida con su sociedad y con su prójimo; a través de una metodología socio constructivista, personalizada y participativa. Esto fundamentado en los avances de la Neuroeducación.', 1),
('vision', 'Nuestra Visión', 'Ser una comunidad educativa consolidada y reconocida como una Institución Educativa privada que contribuye a que todos nuestros estudiantes desarrollen su potencial desde la primera infancia, accedan al mundo letrado, resuelvan problemas, practiquen valores, sepan seguir aprendiendo, se asuman cuidados activos, con derechos y responsabilidades, contribuyendo de forma activa al desarrollo de su comunidad y país.', 2),
('directora', 'Nuestra Directora', 'Lic. Patricia Molina Aguirre - Directora General. Con más de 20 años de experiencia en el ámbito educativo, la Lic. Patricia Molina ha dedicado su vida profesional a la formación integral de niños y jóvenes.', 3);
*/



-- =============================================
-- ÍNDICES PARA OPTIMIZACIÓN
-- =============================================

-- Índices para optimización de consultas
CREATE INDEX idx_usuarios_email ON usuarios(email);
CREATE INDEX idx_usuarios_rol ON usuarios(rol);
CREATE INDEX idx_personas_dni ON personas(dni);
CREATE INDEX idx_estudiantes_grado ON estudiantes(grado_actual);
CREATE INDEX idx_cursos_grado ON cursos(grado);
CREATE INDEX idx_asistencias_fecha ON asistencias_estudiantes(fecha);
CREATE INDEX idx_asistencias_maestros_fecha ON asistencias_maestros(fecha);
CREATE INDEX idx_calificaciones_bimestre ON calificaciones(bimestre_id);
CREATE INDEX idx_mensajes_destinatario ON mensajes(destinatario_id, leido);
CREATE INDEX idx_anuncios_estado ON anuncios(estado);
CREATE INDEX idx_preinscripciones_estado ON preinscripciones(estado);
CREATE INDEX idx_pagos_estado ON pagos(estado);
CREATE INDEX idx_entregas_estado ON entregas(estado);
-- Índices eliminados para tablas que ya no existen
CREATE INDEX idx_horas_trabajadas_maestro ON horas_trabajadas_maestros(maestro_id, fecha);
-- CREATE INDEX idx_estados_cursos ON estados_cursos(curso_id, bimestre_id); -- ELIMINADO: tabla no existe
CREATE INDEX idx_mensajes_tipo_asunto ON mensajes(tipo_asunto);
CREATE INDEX idx_comentarios_padres ON comentarios_padres(estudiante_id, bimestre_id);
CREATE INDEX idx_archivos_justificacion ON archivos_justificacion(justificacion_id);
-- Índice eliminado: resumen_tareas_estudiante (tabla eliminada)
CREATE INDEX idx_cronograma_pagos ON cronograma_pagos(estudiante_id, anio_escolar);
-- CREATE INDEX idx_informacion_institucional ON informacion_institucional(tipo, activo); -- ELIMINADO: tabla no existe
CREATE INDEX idx_calificaciones_examenes ON calificaciones_examenes(estudiante_id, contenido_id);
CREATE INDEX idx_cursos_especialidad ON cursos(especialidad, grado);
-- CREATE INDEX idx_estados_contenido ON estados_contenido(contenido_id, estado); -- ELIMINADO: tabla no existe
CREATE INDEX idx_cursos_especialidad ON cursos(especialidad, grado);
-- CREATE INDEX idx_estados_contenido ON estados_contenido(contenido_id, estado); -- ELIMINADO: tabla no existe
CREATE INDEX idx_contenido_multimedia ON contenido_multimedia(contenido_id, tipo_multimedia);
CREATE INDEX idx_semanas_academicas ON semanas_academicas(curso_id, numero_semana);
CREATE INDEX idx_sesiones_semana ON sesiones(semana_id, curso_id);

-- =============================================
-- TRIGGER PARA VALIDAR ESPECIALIDAD MAESTRO-CURSO
-- =============================================
DELIMITER $

CREATE TRIGGER validar_especialidad_curso_insert
BEFORE INSERT ON cursos
FOR EACH ROW
BEGIN
    DECLARE maestro_especialidad VARCHAR(100);

    -- Obtener la especialidad del maestro
    SELECT especialidad INTO maestro_especialidad
    FROM maestros
    WHERE id = NEW.maestro_id;

    -- Validar que la especialidad del curso coincida con la del maestro
    IF NEW.especialidad != maestro_especialidad THEN
        SIGNAL SQLSTATE '45000'
        SET MESSAGE_TEXT = 'La especialidad del curso debe coincidir con la especialidad del maestro';
    END IF;
END$

CREATE TRIGGER validar_especialidad_curso_update
BEFORE UPDATE ON cursos
FOR EACH ROW
BEGIN
    DECLARE maestro_especialidad VARCHAR(100);

    -- Obtener la especialidad del maestro
    SELECT especialidad INTO maestro_especialidad
    FROM maestros
    WHERE id = NEW.maestro_id;

    -- Validar que la especialidad del curso coincida con la del maestro
    IF NEW.especialidad != maestro_especialidad THEN
        SIGNAL SQLSTATE '45000'
        SET MESSAGE_TEXT = 'La especialidad del curso debe coincidir con la especialidad del maestro';
    END IF;
END$

DELIMITER ;

-- =============================================
-- TABLAS COMPLEMENTARIAS BASADAS EN ANÁLISIS DE ARCHIVOS HTML/JS
-- =============================================

-- =============================================
-- TABLA ELIMINADA: enlaces_videollamadas (DUPLICADA)
-- RAZÓN: Es idéntica a 'enlaces_videoconferencia'
-- =============================================

-- =============================================
-- TABLA ELIMINADA: estados_tareas_estudiantes (INNECESARIA)
-- RAZÓN: El estado se calcula dinámicamente desde tabla 'entregas'
-- =============================================

-- =============================================
-- TABLA ELIMINADA: filtros_tareas (INNECESARIA)
-- RAZÓN: Los filtros se manejan mejor directo en PHP
-- =============================================

-- =============================================
-- TABLA DE HISTORIAL DE MENSAJES RESPONDIDOS
-- =============================================
CREATE TABLE respuestas_mensajes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    mensaje_original_id INT NOT NULL,
    respuesta_id INT NOT NULL,
    fecha_respuesta TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (mensaje_original_id) REFERENCES mensajes(id) ON DELETE CASCADE,
    FOREIGN KEY (respuesta_id) REFERENCES mensajes(id) ON DELETE CASCADE
);

-- =============================================
-- TABLA ELIMINADA: configuracion_graficos (INNECESARIA)
-- RAZÓN: Los gráficos se generan dinámicamente en JavaScript con datos de PHP
-- =============================================

-- =============================================
-- TABLA ELIMINADA: datos_graficos_bimestre
-- RAZÓN: PHP puede calcular estos promedios dinámicamente consultando la tabla 'calificaciones'
-- =============================================

-- =============================================
-- TABLA ELIMINADA: resumen_diario_estudiantes
-- RAZÓN: PHP puede calcular estos datos dinámicamente consultando horarios, asistencias y entregas
-- =============================================

-- =============================================
-- TABLA ELIMINADA: resumen_diario_maestros
-- RAZÓN: PHP puede calcular estos datos dinámicamente consultando mensajes, entregas y asistencias
-- =============================================

-- =============================================
-- TABLA ELIMINADA: horarios_dia_estudiantes
-- RAZÓN: PHP puede obtener los horarios del día consultando 'horarios' + 'inscripciones'
-- =============================================

-- =============================================
-- TABLA ELIMINADA: horarios_dia_maestros
-- RAZÓN: PHP puede obtener los horarios del día consultando 'horarios' + 'cursos'
-- =============================================

-- =============================================
-- TABLA DE TAREAS PRÓXIMAS A VENCER
-- =============================================
CREATE TABLE tareas_proximas_vencer (
    id INT PRIMARY KEY AUTO_INCREMENT,
    contenido_id INT NOT NULL,
    estudiante_id INT NOT NULL,
    dias_restantes INT NOT NULL,
    fecha_calculo DATE NOT NULL,
    notificado BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (contenido_id) REFERENCES contenido(id) ON DELETE CASCADE,
    FOREIGN KEY (estudiante_id) REFERENCES estudiantes(id) ON DELETE CASCADE,
    UNIQUE KEY unique_tarea_proxima (contenido_id, estudiante_id, fecha_calculo)
);

-- =============================================
-- TABLA ELIMINADA: busquedas_frecuentes (INNECESARIA)
-- RAZÓN: Para una escuela pequeña no necesitas analytics de búsquedas
-- =============================================

-- =============================================
-- TABLA ELIMINADA: configuracion_colores_calificaciones (INNECESARIA)
-- RAZÓN: Los colores se manejan mejor en CSS directo
-- =============================================

-- Insertar datos iniciales para filtros de tareas (COMENTADO - TABLA ELIMINADA)
/*
INSERT INTO filtros_tareas (nombre, descripcion) VALUES
('pendientes', 'Tareas pendientes de entrega'),
('completadas', 'Tareas completadas y entregadas'),
('atrasadas', 'Tareas entregadas fuera de plazo'),
('todas', 'Todas las tareas sin filtro');
*/

-- Insertar configuración de gráficos (COMENTADO - TABLA ELIMINADA)
-- Los gráficos se generan dinámicamente en JavaScript con datos de PHP
/*
INSERT INTO configuracion_graficos (tipo_grafico, titulo, descripcion) VALUES
('barras', 'Gráfico de Barras - Promedios por Curso', 'Muestra los promedios de cada curso en formato de barras'),
('lineas', 'Gráfico de Líneas - Evolución por Bimestre', 'Muestra la evolución de calificaciones a través de los bimestres'),
('circular', 'Gráfico Circular - Distribución de Calificaciones', 'Muestra la distribución porcentual de tipos de calificaciones');
*/

-- Insertar configuración de colores (COMENTADO - TABLA ELIMINADA)
-- Los colores se manejan mejor en CSS directo
/*
INSERT INTO configuracion_colores_calificaciones (rango_minimo, rango_maximo, color_hex, descripcion) VALUES
(0.00, 10.99, '#FF0000', 'Desaprobado - Rojo'),
(11.00, 15.99, '#FFA500', 'Regular - Amarillo/Naranja'),
(16.00, 20.00, '#008000', 'Aprobado - Verde');
*/



-- =============================================
-- TABLAS ADICIONALES IDENTIFICADAS EN ANÁLISIS FINAL
-- =============================================

-- =============================================
-- TABLA ELIMINADA: estados_anuncios (INNECESARIA)
-- RAZÓN: Los anuncios ya tienen ENUM directo para estado
-- =============================================

-- =============================================
-- TABLA DE TIPOS DE CONTACTO DE EMERGENCIA
-- =============================================
CREATE TABLE tipos_contacto_emergencia (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nombre VARCHAR(50) NOT NULL, -- 'padre', 'madre', 'tutor_legal', 'familiar', 'otro'
    descripcion VARCHAR(100),
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =============================================
-- TABLA COMENTADA: estados_pagos (NO NECESARIA)
-- RAZÓN: La tabla 'pagos' usa ENUM directamente para los estados
-- =============================================
/*
CREATE TABLE estados_pagos (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nombre VARCHAR(50) NOT NULL, -- 'pendiente', 'pagado', 'vencido'
    descripcion VARCHAR(100),
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
*/

-- =============================================
-- TABLA ELIMINADA: pagos (DUPLICADA - YA EXISTE ANTERIORMENTE)
-- =============================================

-- Insertar estados de anuncios (COMENTADO - TABLA ELIMINADA)
-- Los anuncios ya tienen ENUM directo para estado
/*
INSERT INTO estados_anuncios (nombre, descripcion) VALUES
('borrador', 'Anuncio en estado de borrador'),
('publicado', 'Anuncio publicado y visible');
*/

-- Insertar tipos de contacto de emergencia
INSERT INTO tipos_contacto_emergencia (nombre, descripcion) VALUES
('padre', 'Padre del estudiante'),
('madre', 'Madre del estudiante'),
('tutor_legal', 'Tutor legal del estudiante'),
('familiar', 'Familiar cercano'),
('otro', 'Otro tipo de contacto');

-- Insertar estados de pagos (COMENTADO - NO NECESARIO)
/*
INSERT INTO estados_pagos (nombre, descripcion) VALUES
('pendiente', 'Pago pendiente de realizar'),
('pagado', 'Pago realizado correctamente'),
('vencido', 'Pago vencido sin realizar');
*/

-- =============================================
-- MODIFICACIONES A TABLAS EXISTENTES
-- =============================================

-- Agregar campos adicionales a la tabla anuncios (COMENTADO - REFERENCIA A TABLA ELIMINADA)
-- Los anuncios ya tienen ENUM directo para estado
/*
ALTER TABLE anuncios
ADD COLUMN estado_id INT DEFAULT 2,
ADD COLUMN destacado BOOLEAN DEFAULT FALSE,
ADD FOREIGN KEY (estado_id) REFERENCES estados_anuncios(id);
*/

-- Agregar campos adicionales a la tabla contactos_emergencia
ALTER TABLE contactos_emergencia
ADD COLUMN tipo_contacto_id INT DEFAULT 1,
ADD FOREIGN KEY (tipo_contacto_id) REFERENCES tipos_contacto_emergencia(id);



-- =============================================
-- TABLA ELIMINADA: configuracion_bimestres (DUPLICADA - YA EXISTE 'bimestres')
-- =============================================

-- =============================================
-- TABLA ELIMINADA: enlaces_videoconferencia (DUPLICADA - YA EXISTE ANTERIORMENTE)
-- =============================================

-- Insertar configuración de bimestres (COMENTADO - TABLA ELIMINADA)
-- Los datos ya están insertados en la tabla 'bimestres'
/*
INSERT INTO configuracion_bimestres (numero_bimestre, nombre, fecha_inicio, fecha_fin, año_escolar) VALUES
(1, 'Primer Bimestre', '2025-03-01', '2025-04-30', 2025),
(2, 'Segundo Bimestre', '2025-05-01', '2025-06-30', 2025),
(3, 'Tercer Bimestre', '2025-08-01', '2025-09-30', 2025),
(4, 'Cuarto Bimestre', '2025-10-01', '2025-12-15', 2025);
*/

-- =============================================
-- TABLA ELIMINADA: actividades_sistema (DUPLICADA - YA EXISTE ANTERIORMENTE)
-- =============================================

-- =============================================
-- TABLA ELIMINADA: cuotas (DUPLICADA - YA EXISTE 'pagos')
-- RAZÓN: La tabla 'pagos' ya maneja matrícula y pensiones
-- =============================================

-- =============================================
-- TABLA ELIMINADA: justificaciones_asistencia (DUPLICADA - YA EXISTE 'justificaciones_maestros')
-- =============================================



-- =============================================
-- TABLA DE RANKING DE ESTUDIANTES
-- =============================================
CREATE TABLE ranking_estudiantes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    estudiante_id INT NOT NULL,
    grado VARCHAR(20) NOT NULL, -- Cambiado de grado_id a grado directo
    bimestre INT NOT NULL,
    año_escolar INT NOT NULL,
    promedio_general DECIMAL(4,2) NOT NULL,
    posicion INT NOT NULL,
    total_estudiantes INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (estudiante_id) REFERENCES estudiantes(id) ON DELETE CASCADE,
    UNIQUE KEY unique_ranking (estudiante_id, grado, bimestre, año_escolar)
);



-- =============================================
-- ÍNDICES ELIMINADOS: DUPLICADOS (YA EXISTEN ANTERIORMENTE)
-- =============================================

-- =============================================
-- VISTAS ÚTILES PARA PHP
-- =============================================

-- Vista para obtener información completa de estudiantes
CREATE VIEW vista_estudiantes_completa AS
SELECT
    e.id,
    p.nombres,
    p.apellido_paterno,
    p.apellido_materno,
    CONCAT(p.nombres, ' ', p.apellido_paterno, ' ', p.apellido_materno) as nombre_completo,
    e.grado_actual as grado,
    CASE
        WHEN e.grado_actual LIKE 'inicial%' THEN 'inicial'
        WHEN e.grado_actual LIKE 'primaria%' THEN 'primaria'
        ELSE 'otro'
    END as nivel,
    u.nombre_usuario,
    u.email,
    p.fecha_nacimiento,
    p.sexo,
    p.direccion,
    p.telefono,
    e.created_at as fecha_registro
FROM estudiantes e
JOIN personas p ON e.persona_id = p.id
JOIN usuarios u ON p.usuario_id = u.id
WHERE u.activo = TRUE;

-- Vista para obtener información completa de maestros
CREATE VIEW vista_maestros_completa AS
SELECT
    m.id,
    p.nombres,
    p.apellido_paterno,
    p.apellido_materno,
    CONCAT(p.nombres, ' ', p.apellido_paterno, ' ', p.apellido_materno) as nombre_completo,
    m.especialidad,
    m.nivel_educativo,
    m.fecha_contratacion,
    u.nombre_usuario,
    u.email,
    p.fecha_nacimiento,
    p.sexo,
    p.direccion,
    p.telefono
FROM maestros m
JOIN personas p ON m.persona_id = p.id
JOIN usuarios u ON p.usuario_id = u.id
WHERE u.activo = TRUE;

-- Vista para calificaciones con información del estudiante y curso
CREATE VIEW vista_calificaciones_completa AS
SELECT
    c.id,
    CONCAT(p.nombres, ' ', p.apellido_paterno, ' ', p.apellido_materno) as estudiante,
    cur.nombre as curso,
    c.bimestre_id,
    c.promedio_tareas,
    c.promedio_examenes,
    c.promedio_participacion,
    c.calificacion_final,
    c.comentario_maestro,
    e.grado_actual as grado
FROM calificaciones c
JOIN estudiantes e ON c.estudiante_id = e.id
JOIN personas p ON e.persona_id = p.id
JOIN cursos cur ON c.curso_id = cur.id;

-- =============================================
-- PROCEDIMIENTOS ALMACENADOS ÚTILES PARA PHP
-- =============================================

DELIMITER //

-- Procedimiento para calcular promedio de estudiante por bimestre
CREATE PROCEDURE CalcularPromedioEstudiante(
    IN p_estudiante_id INT,
    IN p_bimestre INT,
    IN p_año_escolar INT
)
BEGIN
    DECLARE promedio_general DECIMAL(4,2);

    SELECT AVG(promedio_final) INTO promedio_general
    FROM calificaciones
    WHERE estudiante_id = p_estudiante_id
    AND bimestre = p_bimestre;

    -- Actualizar o insertar en ranking
    INSERT INTO ranking_estudiantes (estudiante_id, grado, bimestre, año_escolar, promedio_general, posicion, total_estudiantes)
    SELECT
        p_estudiante_id,
        e.grado_actual,
        p_bimestre,
        p_año_escolar,
        promedio_general,
        1, -- Se calculará la posición después
        1  -- Se calculará el total después
    FROM estudiantes e
    WHERE e.id = p_estudiante_id
    ON DUPLICATE KEY UPDATE
        promedio_general = promedio_general,
        updated_at = CURRENT_TIMESTAMP;

    SELECT promedio_general as resultado;
END //

-- Procedimiento para obtener estadísticas del dashboard de administrador
CREATE PROCEDURE EstadisticasAdmin()
BEGIN
    SELECT
        (SELECT COUNT(*) FROM usuarios WHERE activo = TRUE) as total_usuarios,
        (SELECT COUNT(*) FROM estudiantes e JOIN usuarios u ON e.usuario_id = u.id WHERE u.activo = TRUE) as total_estudiantes,
        (SELECT COUNT(*) FROM maestros m JOIN usuarios u ON m.usuario_id = u.id WHERE u.activo = TRUE) as total_maestros,
        (SELECT COUNT(*) FROM preinscripciones WHERE revisado = FALSE) as solicitudes_pendientes,
        (SELECT COUNT(*) FROM anuncios WHERE estado = 'borrador') as anuncios_borrador;
END //

DELIMITER ;


-- Índices adicionales para las nuevas tablas
-- CREATE INDEX idx_enlaces_videollamadas ON enlaces_videollamadas(curso_id, fecha); -- ELIMINADO: tabla duplicada
-- CREATE INDEX idx_estados_tareas_estudiantes ON estados_tareas_estudiantes(estudiante_id, estado); -- ELIMINADO: tabla no existe
CREATE INDEX idx_respuestas_mensajes ON respuestas_mensajes(mensaje_original_id);
-- Índices eliminados para tablas que ya no existen
CREATE INDEX idx_tareas_proximas_vencer ON tareas_proximas_vencer(estudiante_id, dias_restantes);
-- CREATE INDEX idx_busquedas_frecuentes ON busquedas_frecuentes(usuario_id, tipo_busqueda); -- ELIMINADO: tabla no existe
-- CREATE INDEX idx_configuracion_colores ON configuracion_colores_calificaciones(rango_minimo, rango_maximo); -- ELIMINADO: tabla no existe

-- =============================================
-- RESUMEN FINAL DE LA BASE DE DATOS
-- =============================================

/*
TOTAL DE TABLAS ACTIVAS: 37 tablas funcionales

TABLAS ELIMINADAS PARA OPTIMIZACIÓN CON PHP/JS:
- ❌ filtros_tareas: Los filtros se manejan mejor directo en PHP
- ❌ filtros_anuncios: Los filtros se manejan mejor directo en PHP
- ❌ busquedas_frecuentes: Innecesario para una escuela pequeña
- ❌ configuracion_colores_calificaciones: Los colores van mejor en CSS
- ❌ iconos_cursos: Los iconos van directo en tabla 'cursos'
- ❌ informacion_institucional: Va mejor en archivos estáticos
- ❌ configuracion_graficos: Los gráficos se generan dinámicamente en JavaScript con datos de PHP
- ❌ enlaces_videollamadas: Duplicada de 'enlaces_videoconferencia'
- ❌ estados_pagos: La tabla 'pagos' usa ENUM directamente
- ❌ estados_contenido: El contenido no necesita estados, se maneja con fechas límite
- ❌ estados_cursos: El estado del curso se calcula dinámicamente en PHP
- ❌ estados_tareas_estudiantes: El estado se calcula dinámicamente desde tabla 'entregas'
- ❌ estados_anuncios: Los anuncios ya tienen ENUM directo para estado

FUNCIONALIDADES CUBIERTAS:
✅ Sistema de 4 bimestres configurado
✅ Preinscripciones completas con todos los campos requeridos
✅ Anuncios con estados borrador/publicado
✅ Cursos con iconos Material Icons compatibles con HTML
✅ Calificaciones separadas por tareas, exámenes y participación
✅ Contenido multimedia con 8 tipos diferentes
✅ Asistencias completas para estudiantes y maestros
✅ Mensajería entre todos los roles
✅ Sistema de pagos manual como especificado
✅ Dashboards específicos para cada rol
✅ Justificaciones de asistencia para maestros
✅ Enlaces de videoconferencia para clases virtuales

OPTIMIZADO PARA:
✅ PHP MVC - Estructura ideal para desarrollo
✅ JavaScript - Gráficos dinámicos sin tablas de configuración
✅ CSS - Colores y estilos sin dependencias de BD
✅ Material Icons - Iconos compatibles con archivos HTML

BASE DE DATOS LISTA PARA IMPLEMENTACIÓN EN MYSQL
*/

