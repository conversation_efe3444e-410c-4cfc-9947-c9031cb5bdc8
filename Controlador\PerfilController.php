<?php
// Habilitar reporte de errores para debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Log de inicio
error_log("PerfilController: Iniciando procesamiento - " . date('Y-m-d H:i:s'));

require_once '../Modelo/Conexion.php';
require_once 'AuthController.php';

class PerfilController {
    private $pdo;

    public function __construct() {
        try {
            $this->pdo = Conexion::getConexion();
            error_log("PerfilController: Conexión a BD exitosa");
        } catch (Exception $e) {
            error_log("PerfilController: Error en conexión BD - " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Procesa las solicitudes de actualización de perfil
     */
    public function procesarSolicitud() {
        error_log("PerfilController: procesarSolicitud iniciado");
        error_log("PerfilController: Mé<PERSON>do HTTP - " . $_SERVER['REQUEST_METHOD']);
        error_log("PerfilController: POST data - " . print_r($_POST, true));

        // Verificar que el usuario esté autenticado
        if (!AuthController::estaAutenticado()) {
            error_log("PerfilController: Usuario no autenticado");
            $this->enviarRespuesta(false, 'Usuario no autenticado');
            return;
        }

        // Verificar método POST
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            error_log("PerfilController: Método no permitido - " . $_SERVER['REQUEST_METHOD']);
            $this->enviarRespuesta(false, 'Método no permitido');
            return;
        }

        // Obtener el tipo de actualización
        $tipoActualizacion = $_POST['tipo_actualizacion'] ?? '';
        error_log("PerfilController: Tipo de actualización - " . $tipoActualizacion);

        switch ($tipoActualizacion) {
            case 'personal':
                $this->actualizarInformacionPersonal();
                break;
            case 'account':
                $this->actualizarInformacionCuenta();
                break;
            case 'professional':
                $this->actualizarInformacionProfesional();
                break;
            case 'emergency':
                $this->actualizarContactoEmergencia();
                break;
            case 'password':
                $this->cambiarContrasena();
                break;
            default:
                error_log("PerfilController: Tipo de actualización no válido - " . $tipoActualizacion);
                $this->enviarRespuesta(false, 'Tipo de actualización no válido: ' . $tipoActualizacion);
        }
    }
    
    /**
     * Actualiza la información personal del usuario
     */
    private function actualizarInformacionPersonal() {
        try {
            $usuarioActual = AuthController::obtenerUsuarioActual();
            
            // Validar campos requeridos
            $nombres = trim($_POST['nombres'] ?? '');
            $apellido_paterno = trim($_POST['apellido_paterno'] ?? '');
            $apellido_materno = trim($_POST['apellido_materno'] ?? '');
            $dni = trim($_POST['dni'] ?? '');
            $fecha_nacimiento = $_POST['fecha_nacimiento'] ?? null;
            $sexo = $_POST['sexo'] ?? '';
            $direccion = trim($_POST['direccion'] ?? '');
            $telefono = trim($_POST['telefono'] ?? '');
            
            if (empty($nombres) || empty($apellido_paterno) || empty($apellido_materno)) {
                $this->enviarRespuesta(false, 'Los nombres y apellidos son obligatorios');
                return;
            }
            
            // Validar DNI si se proporciona
            if (!empty($dni) && !preg_match('/^\d{8}$/', $dni)) {
                $this->enviarRespuesta(false, 'El DNI debe tener 8 dígitos');
                return;
            }
            
            // Calcular edad si hay fecha de nacimiento
            $edad = null;
            if (!empty($fecha_nacimiento)) {
                $fechaNac = new DateTime($fecha_nacimiento);
                $hoy = new DateTime();
                $edad = $hoy->diff($fechaNac)->y;
            }
            
            $this->pdo->beginTransaction();
            
            // Actualizar tabla personas
            $sql = "UPDATE personas SET 
                        nombres = :nombres,
                        apellido_paterno = :apellido_paterno,
                        apellido_materno = :apellido_materno,
                        dni = :dni,
                        fecha_nacimiento = :fecha_nacimiento,
                        edad = :edad,
                        sexo = :sexo,
                        direccion = :direccion,
                        telefono = :telefono,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE usuario_id = :usuario_id";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([
                ':nombres' => $nombres,
                ':apellido_paterno' => $apellido_paterno,
                ':apellido_materno' => $apellido_materno,
                ':dni' => $dni ?: null,
                ':fecha_nacimiento' => $fecha_nacimiento ?: null,
                ':edad' => $edad,
                ':sexo' => $sexo,
                ':direccion' => $direccion,
                ':telefono' => $telefono,
                ':usuario_id' => $usuarioActual['id']
            ]);
            
            // Actualizar sesión con los nuevos datos
            $_SESSION['nombres'] = $nombres;
            $_SESSION['apellido_paterno'] = $apellido_paterno;
            $_SESSION['apellido_materno'] = $apellido_materno;
            
            $this->pdo->commit();
            $this->enviarRespuesta(true, 'Información personal actualizada correctamente');
            
        } catch (Exception $e) {
            $this->pdo->rollBack();
            $this->enviarRespuesta(false, 'Error al actualizar información personal: ' . $e->getMessage());
        }
    }
    
    /**
     * Actualiza la información de cuenta del usuario
     */
    private function actualizarInformacionCuenta() {
        try {
            $usuarioActual = AuthController::obtenerUsuarioActual();
            
            $nombre_usuario = trim($_POST['nombre_usuario'] ?? '');
            $email = trim($_POST['email'] ?? '');
            
            if (empty($nombre_usuario) || empty($email)) {
                $this->enviarRespuesta(false, 'El nombre de usuario y email son obligatorios');
                return;
            }
            
            // Validar email
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $this->enviarRespuesta(false, 'El formato del email no es válido');
                return;
            }
            
            // Verificar que el nombre de usuario no esté en uso por otro usuario
            $sqlCheck = "SELECT id FROM usuarios WHERE nombre_usuario = :nombre_usuario AND id != :usuario_id";
            $stmtCheck = $this->pdo->prepare($sqlCheck);
            $stmtCheck->execute([':nombre_usuario' => $nombre_usuario, ':usuario_id' => $usuarioActual['id']]);
            
            if ($stmtCheck->fetch()) {
                $this->enviarRespuesta(false, 'El nombre de usuario ya está en uso');
                return;
            }
            
            // Verificar que el email no esté en uso por otro usuario
            $sqlCheck = "SELECT id FROM usuarios WHERE email = :email AND id != :usuario_id";
            $stmtCheck = $this->pdo->prepare($sqlCheck);
            $stmtCheck->execute([':email' => $email, ':usuario_id' => $usuarioActual['id']]);
            
            if ($stmtCheck->fetch()) {
                $this->enviarRespuesta(false, 'El email ya está en uso');
                return;
            }
            
            $this->pdo->beginTransaction();
            
            // Actualizar tabla usuarios
            $sql = "UPDATE usuarios SET 
                        nombre_usuario = :nombre_usuario,
                        email = :email,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = :usuario_id";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([
                ':nombre_usuario' => $nombre_usuario,
                ':email' => $email,
                ':usuario_id' => $usuarioActual['id']
            ]);
            
            // Actualizar sesión
            $_SESSION['nombre_usuario'] = $nombre_usuario;
            $_SESSION['email'] = $email;
            
            $this->pdo->commit();
            $this->enviarRespuesta(true, 'Información de cuenta actualizada correctamente');
            
        } catch (Exception $e) {
            $this->pdo->rollBack();
            $this->enviarRespuesta(false, 'Error al actualizar información de cuenta: ' . $e->getMessage());
        }
    }
    
    /**
     * Actualiza la información profesional del administrador
     */
    private function actualizarInformacionProfesional() {
        try {
            $usuarioActual = AuthController::obtenerUsuarioActual();
            
            // Verificar que el usuario sea administrador
            if ($usuarioActual['rol'] !== 'administrador') {
                $this->enviarRespuesta(false, 'No tienes permisos para esta acción');
                return;
            }
            
            $cargo = trim($_POST['cargo'] ?? '');
            $departamento = trim($_POST['departamento'] ?? '');
            $fecha_contratacion = $_POST['fecha_contratacion'] ?? null;
            
            if (empty($cargo)) {
                $this->enviarRespuesta(false, 'El cargo es obligatorio');
                return;
            }
            
            $this->pdo->beginTransaction();
            
            // Obtener persona_id del administrador
            $sqlPersona = "SELECT id FROM personas WHERE usuario_id = :usuario_id";
            $stmtPersona = $this->pdo->prepare($sqlPersona);
            $stmtPersona->execute([':usuario_id' => $usuarioActual['id']]);
            $persona = $stmtPersona->fetch();
            
            if (!$persona) {
                $this->enviarRespuesta(false, 'No se encontró información de persona');
                return;
            }
            
            // Actualizar tabla administradores
            $sql = "UPDATE administradores SET 
                        cargo = :cargo,
                        departamento = :departamento,
                        fecha_contratacion = :fecha_contratacion,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE persona_id = :persona_id";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([
                ':cargo' => $cargo,
                ':departamento' => $departamento,
                ':fecha_contratacion' => $fecha_contratacion ?: null,
                ':persona_id' => $persona['id']
            ]);
            
            $this->pdo->commit();
            $this->enviarRespuesta(true, 'Información profesional actualizada correctamente');
            
        } catch (Exception $e) {
            $this->pdo->rollBack();
            $this->enviarRespuesta(false, 'Error al actualizar información profesional: ' . $e->getMessage());
        }
    }
    
    /**
     * Actualiza o crea el contacto de emergencia
     */
    private function actualizarContactoEmergencia() {
        try {
            $usuarioActual = AuthController::obtenerUsuarioActual();
            
            $nombre_contacto = trim($_POST['nombre_contacto'] ?? '');
            $telefono_principal = trim($_POST['telefono_principal'] ?? '');
            $telefono_alternativo = trim($_POST['telefono_alternativo'] ?? '');
            $email = trim($_POST['email'] ?? '');
            
            if (empty($nombre_contacto) || empty($telefono_principal)) {
                $this->enviarRespuesta(false, 'El nombre del contacto y teléfono principal son obligatorios');
                return;
            }
            
            // Validar email si se proporciona
            if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $this->enviarRespuesta(false, 'El formato del email no es válido');
                return;
            }
            
            $this->pdo->beginTransaction();
            
            // Obtener persona_id
            $sqlPersona = "SELECT id FROM personas WHERE usuario_id = :usuario_id";
            $stmtPersona = $this->pdo->prepare($sqlPersona);
            $stmtPersona->execute([':usuario_id' => $usuarioActual['id']]);
            $persona = $stmtPersona->fetch();
            
            if (!$persona) {
                $this->enviarRespuesta(false, 'No se encontró información de persona');
                return;
            }
            
            // Verificar si ya existe un contacto de emergencia
            $sqlCheck = "SELECT id FROM contactos_emergencia WHERE persona_id = :persona_id";
            $stmtCheck = $this->pdo->prepare($sqlCheck);
            $stmtCheck->execute([':persona_id' => $persona['id']]);
            $existeContacto = $stmtCheck->fetch();
            
            if ($existeContacto) {
                // Actualizar contacto existente
                $sql = "UPDATE contactos_emergencia SET 
                            nombre_contacto = :nombre_contacto,
                            telefono_principal = :telefono_principal,
                            telefono_alternativo = :telefono_alternativo,
                            email = :email,
                            updated_at = CURRENT_TIMESTAMP
                        WHERE persona_id = :persona_id";
            } else {
                // Crear nuevo contacto
                $sql = "INSERT INTO contactos_emergencia 
                        (persona_id, nombre_contacto, telefono_principal, telefono_alternativo, email, tipo_contacto_id) 
                        VALUES (:persona_id, :nombre_contacto, :telefono_principal, :telefono_alternativo, :email, 5)";
            }
            
            $stmt = $this->pdo->prepare($sql);
            $params = [
                ':persona_id' => $persona['id'],
                ':nombre_contacto' => $nombre_contacto,
                ':telefono_principal' => $telefono_principal,
                ':telefono_alternativo' => $telefono_alternativo ?: null,
                ':email' => $email ?: null
            ];
            
            $stmt->execute($params);
            
            $this->pdo->commit();
            $this->enviarRespuesta(true, 'Contacto de emergencia actualizado correctamente');
            
        } catch (Exception $e) {
            $this->pdo->rollBack();
            $this->enviarRespuesta(false, 'Error al actualizar contacto de emergencia: ' . $e->getMessage());
        }
    }
    
    /**
     * Cambia la contraseña del usuario
     */
    private function cambiarContrasena() {
        try {
            $usuarioActual = AuthController::obtenerUsuarioActual();
            
            $contrasena_actual = $_POST['contrasena_actual'] ?? '';
            $nueva_contrasena = $_POST['nueva_contrasena'] ?? '';
            $confirmar_contrasena = $_POST['confirmar_contrasena'] ?? '';
            
            if (empty($contrasena_actual) || empty($nueva_contrasena) || empty($confirmar_contrasena)) {
                $this->enviarRespuesta(false, 'Todos los campos son obligatorios');
                return;
            }
            
            if ($nueva_contrasena !== $confirmar_contrasena) {
                $this->enviarRespuesta(false, 'Las contraseñas no coinciden');
                return;
            }
            
            // Validar fortaleza de la contraseña
            if (strlen($nueva_contrasena) < 8) {
                $this->enviarRespuesta(false, 'La contraseña debe tener al menos 8 caracteres');
                return;
            }
            
            if (!preg_match('/[A-Z]/', $nueva_contrasena)) {
                $this->enviarRespuesta(false, 'La contraseña debe contener al menos una letra mayúscula');
                return;
            }
            
            if (!preg_match('/[0-9]/', $nueva_contrasena)) {
                $this->enviarRespuesta(false, 'La contraseña debe contener al menos un número');
                return;
            }
            
            if (!preg_match('/[^a-zA-Z0-9]/', $nueva_contrasena)) {
                $this->enviarRespuesta(false, 'La contraseña debe contener al menos un carácter especial');
                return;
            }
            
            // Verificar contraseña actual
            $sqlUsuario = "SELECT password FROM usuarios WHERE id = :usuario_id";
            $stmtUsuario = $this->pdo->prepare($sqlUsuario);
            $stmtUsuario->execute([':usuario_id' => $usuarioActual['id']]);
            $usuario = $stmtUsuario->fetch();
            
            if (!$usuario || !password_verify($contrasena_actual, $usuario['password'])) {
                $this->enviarRespuesta(false, 'La contraseña actual es incorrecta');
                return;
            }
            
            $this->pdo->beginTransaction();
            
            // Actualizar contraseña
            $nueva_contrasena_hash = password_hash($nueva_contrasena, PASSWORD_DEFAULT);
            $sql = "UPDATE usuarios SET 
                        password = :password,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = :usuario_id";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([
                ':password' => $nueva_contrasena_hash,
                ':usuario_id' => $usuarioActual['id']
            ]);
            
            $this->pdo->commit();
            $this->enviarRespuesta(true, 'Contraseña cambiada correctamente');
            
        } catch (Exception $e) {
            $this->pdo->rollBack();
            $this->enviarRespuesta(false, 'Error al cambiar contraseña: ' . $e->getMessage());
        }
    }
    
    /**
     * Envía una respuesta JSON
     */
    private function enviarRespuesta($success, $message, $data = null) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => $success,
            'message' => $message,
            'data' => $data
        ]);
        exit;
    }
}

// Procesar la solicitud si se accede directamente al archivo
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $controller = new PerfilController();
    $controller->procesarSolicitud();
}
?>
